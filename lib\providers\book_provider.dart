import 'package:flutter/material.dart';
import '../models/book.dart';
import '../services/database_service.dart';

class BookProvider extends ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();
  List<Book> _books = [];
  bool _isLoading = false;

  List<Book> get books => _books;
  bool get isLoading => _isLoading;

  BookProvider() {
    loadBooks();
  }

  Future<void> loadBooks() async {
    _isLoading = true;
    notifyListeners();

    try {
      _books = await _databaseService.getBooks();
    } catch (e) {
      debugPrint('Error loading books: $e');
    }

    _isLoading = false;
    notifyListeners();
  }

  Future<void> addBook(Book book) async {
    try {
      await _databaseService.insertBook(book);
      _books.add(book);
      notifyListeners();
    } catch (e) {
      debugPrint('Error adding book: $e');
    }
  }

  Future<void> updateBook(Book book) async {
    try {
      await _databaseService.updateBook(book);
      final index = _books.indexWhere((b) => b.id == book.id);
      if (index != -1) {
        _books[index] = book;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error updating book: $e');
    }
  }

  Future<void> deleteBook(String bookId) async {
    try {
      await _databaseService.deleteBook(bookId);
      _books.removeWhere((b) => b.id == bookId);
      notifyListeners();
    } catch (e) {
      debugPrint('Error deleting book: $e');
    }
  }

  Future<void> updateProgress(String bookId, int currentPage) async {
    try {
      final bookIndex = _books.indexWhere((b) => b.id == bookId);
      if (bookIndex != -1) {
        final book = _books[bookIndex];
        final updatedBook = book.copyWith(
          currentPage: currentPage,
          updatedAt: DateTime.now(),
          completedAt: currentPage >= book.totalPages ? DateTime.now() : null,
        );
        
        await updateBook(updatedBook);
      }
    } catch (e) {
      debugPrint('Error updating book progress: $e');
    }
  }

  List<Book> getActiveBooks() {
    return _books.where((book) => book.isActive && !book.isCompleted).toList();
  }

  List<Book> getCompletedBooks() {
    return _books.where((book) => book.isCompleted).toList();
  }

  int getTotalBooksCount() => _books.length;
  int getActiveBooksCount() => getActiveBooks().length;
  int getCompletedBooksCount() => getCompletedBooks().length;

  double getOverallProgress() {
    if (_books.isEmpty) return 0.0;
    
    double totalProgress = 0.0;
    for (final book in _books) {
      totalProgress += book.progressPercentage;
    }
    
    return totalProgress / _books.length;
  }
}
