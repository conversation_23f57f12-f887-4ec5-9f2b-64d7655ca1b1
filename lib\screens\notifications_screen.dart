import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../providers/theme_provider.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  bool _routineNotifications = true;
  bool _quranNotifications = true;
  bool _readingNotifications = true;
  bool _dailyReminders = true;
  
  TimeOfDay _morningReminderTime = const TimeOfDay(hour: 8, minute: 0);
  TimeOfDay _eveningReminderTime = const TimeOfDay(hour: 20, minute: 0);

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _routineNotifications = prefs.getBool('routine_notifications') ?? true;
      _quranNotifications = prefs.getBool('quran_notifications') ?? true;
      _readingNotifications = prefs.getBool('reading_notifications') ?? true;
      _dailyReminders = prefs.getBool('daily_reminders') ?? true;
      
      final morningHour = prefs.getInt('morning_reminder_hour') ?? 8;
      final morningMinute = prefs.getInt('morning_reminder_minute') ?? 0;
      _morningReminderTime = TimeOfDay(hour: morningHour, minute: morningMinute);
      
      final eveningHour = prefs.getInt('evening_reminder_hour') ?? 20;
      final eveningMinute = prefs.getInt('evening_reminder_minute') ?? 0;
      _eveningReminderTime = TimeOfDay(hour: eveningHour, minute: eveningMinute);
    });
  }

  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('routine_notifications', _routineNotifications);
    await prefs.setBool('quran_notifications', _quranNotifications);
    await prefs.setBool('reading_notifications', _readingNotifications);
    await prefs.setBool('daily_reminders', _dailyReminders);
    
    await prefs.setInt('morning_reminder_hour', _morningReminderTime.hour);
    await prefs.setInt('morning_reminder_minute', _morningReminderTime.minute);
    await prefs.setInt('evening_reminder_hour', _eveningReminderTime.hour);
    await prefs.setInt('evening_reminder_minute', _eveningReminderTime.minute);
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        final theme = themeProvider.currentTheme;
        
        return Scaffold(
          appBar: AppBar(
            title: Text(
              'إعدادات الإشعارات',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: theme.primaryText,
              ),
            ),
            backgroundColor: theme.primaryBackground,
            elevation: 0,
          ),
          body: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildNotificationTypesSection(theme),
                const SizedBox(height: 24),
                _buildReminderTimesSection(theme),
                const SizedBox(height: 24),
                _buildInfoSection(theme),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildNotificationTypesSection(theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'أنواع الإشعارات',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: theme.primaryText,
              ),
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: Text(
                'إشعارات الروتينات',
                style: TextStyle(color: theme.primaryText),
              ),
              subtitle: Text(
                'تذكير بالروتينات اليومية',
                style: TextStyle(color: theme.secondaryText),
              ),
              value: _routineNotifications,
              onChanged: (value) {
                setState(() {
                  _routineNotifications = value;
                });
                _saveSettings();
              },
              activeColor: theme.progressGradient.first,
            ),
            SwitchListTile(
              title: Text(
                'إشعارات القرآن',
                style: TextStyle(color: theme.primaryText),
              ),
              subtitle: Text(
                'تذكير بخطط التلاوة والحفظ',
                style: TextStyle(color: theme.secondaryText),
              ),
              value: _quranNotifications,
              onChanged: (value) {
                setState(() {
                  _quranNotifications = value;
                });
                _saveSettings();
              },
              activeColor: theme.progressGradient.first,
            ),
            SwitchListTile(
              title: Text(
                'إشعارات القراءة',
                style: TextStyle(color: theme.primaryText),
              ),
              subtitle: Text(
                'تذكير بقراءة الكتب',
                style: TextStyle(color: theme.secondaryText),
              ),
              value: _readingNotifications,
              onChanged: (value) {
                setState(() {
                  _readingNotifications = value;
                });
                _saveSettings();
              },
              activeColor: theme.progressGradient.first,
            ),
            SwitchListTile(
              title: Text(
                'التذكيرات اليومية',
                style: TextStyle(color: theme.primaryText),
              ),
              subtitle: Text(
                'تذكير عام بالمهام اليومية',
                style: TextStyle(color: theme.secondaryText),
              ),
              value: _dailyReminders,
              onChanged: (value) {
                setState(() {
                  _dailyReminders = value;
                });
                _saveSettings();
              },
              activeColor: theme.progressGradient.first,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReminderTimesSection(theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'أوقات التذكير',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: theme.primaryText,
              ),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: Icon(
                Icons.wb_sunny,
                color: theme.progressGradient.first,
              ),
              title: Text(
                'تذكير الصباح',
                style: TextStyle(color: theme.primaryText),
              ),
              subtitle: Text(
                'للروتينات الصباحية',
                style: TextStyle(color: theme.secondaryText),
              ),
              trailing: Text(
                _formatTime(_morningReminderTime),
                style: TextStyle(
                  color: theme.primaryText,
                  fontWeight: FontWeight.w600,
                ),
              ),
              onTap: () => _selectTime(context, true),
            ),
            const Divider(),
            ListTile(
              leading: Icon(
                Icons.nightlight_round,
                color: theme.progressGradient.first,
              ),
              title: Text(
                'تذكير المساء',
                style: TextStyle(color: theme.primaryText),
              ),
              subtitle: Text(
                'للروتينات المسائية',
                style: TextStyle(color: theme.secondaryText),
              ),
              trailing: Text(
                _formatTime(_eveningReminderTime),
                style: TextStyle(
                  color: theme.primaryText,
                  fontWeight: FontWeight.w600,
                ),
              ),
              onTap: () => _selectTime(context, false),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoSection(theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: theme.primaryText,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'الإشعارات تساعدك على الالتزام بروتيناتك اليومية وتحقيق أهدافك. يمكنك تخصيص الأوقات والأنواع حسب احتياجاتك.',
              style: TextStyle(
                color: theme.secondaryText,
                height: 1.5,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'ملاحظة: تأكد من السماح للتطبيق بإرسال الإشعارات من إعدادات النظام.',
              style: TextStyle(
                color: theme.progressGradient.first,
                fontWeight: FontWeight.w500,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatTime(TimeOfDay time) {
    final hour = time.hour;
    final minute = time.minute;
    final period = hour >= 12 ? 'مساءً' : 'صباحاً';
    final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);
    
    return '${displayHour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $period';
  }

  Future<void> _selectTime(BuildContext context, bool isMorning) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: isMorning ? _morningReminderTime : _eveningReminderTime,
      builder: (context, child) {
        return Directionality(
          textDirection: TextDirection.rtl,
          child: child!,
        );
      },
    );
    
    if (picked != null) {
      setState(() {
        if (isMorning) {
          _morningReminderTime = picked;
        } else {
          _eveningReminderTime = picked;
        }
      });
      _saveSettings();
    }
  }
}
