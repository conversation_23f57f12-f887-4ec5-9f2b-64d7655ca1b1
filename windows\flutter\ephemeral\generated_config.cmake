# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\flutter-sdk" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\BrmGah\\roteyn\\augment\\augment_flutter_11" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\Users\\<USER>\\flutter-sdk"
  "PROJECT_DIR=D:\\BrmGah\\roteyn\\augment\\augment_flutter_11"
  "FLUTTER_ROOT=C:\\Users\\<USER>\\flutter-sdk"
  "FLUTTER_EPHEMERAL_DIR=D:\\BrmGah\\roteyn\\augment\\augment_flutter_11\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\BrmGah\\roteyn\\augment\\augment_flutter_11"
  "FLUTTER_TARGET=D:\\BrmGah\\roteyn\\augment\\augment_flutter_11\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\BrmGah\\roteyn\\augment\\augment_flutter_11\\.dart_tool\\package_config.json"
)
