import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';
import '../providers/quran_provider.dart';
import '../models/quran.dart';

class QuranScreen extends StatefulWidget {
  const QuranScreen({super.key});

  @override
  State<QuranScreen> createState() => _QuranScreenState();
}

class _QuranScreenState extends State<QuranScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  String _currentSection = 'recitation';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _tabController.addListener(() {
      setState(() {
        switch (_tabController.index) {
          case 0:
            _currentSection = 'recitation';
            break;
          case 1:
            _currentSection = 'memorization';
            break;
          case 2:
            _currentSection = 'review';
            break;
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<ThemeProvider, QuranProvider>(
      builder: (context, themeProvider, quranProvider, child) {
        final theme = themeProvider.currentTheme;

        return Scaffold(
          appBar: AppBar(
            title: Text(
              'رحلة القرآن',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: theme.primaryText,
              ),
            ),
            backgroundColor: theme.primaryBackground,
            elevation: 0,
            bottom: TabBar(
              controller: _tabController,
              tabs: const [
                Tab(text: 'التلاوة'),
                Tab(text: 'الحفظ'),
                Tab(text: 'المراجعة'),
              ],
              labelColor: theme.progressGradient.first,
              unselectedLabelColor: theme.secondaryText,
              indicatorColor: theme.progressGradient.first,
            ),
          ),
          body: Column(
            children: [
              _buildProgressSection(theme, quranProvider),
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildQuranGrid(theme, quranProvider, 'recitation'),
                    _buildQuranGrid(theme, quranProvider, 'memorization'),
                    _buildQuranGrid(theme, quranProvider, 'review'),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildProgressSection(theme, QuranProvider quranProvider) {
    double progress;
    String progressText;

    switch (_currentSection) {
      case 'recitation':
        progress = quranProvider.getRecitationProgress();
        progressText = 'التلاوة: ${progress.toStringAsFixed(1)}%';
        break;
      case 'memorization':
        progress = quranProvider.getMemorizationProgress();
        progressText = 'الحفظ: ${progress.toStringAsFixed(1)}%';
        break;
      case 'review':
        progress = quranProvider.getReviewProgress();
        progressText = 'المراجعة: ${progress.toStringAsFixed(1)}%';
        break;
      default:
        progress = 0;
        progressText = '';
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.cardBackground,
        border: Border(bottom: BorderSide(color: theme.borderColor, width: 1)),
      ),
      child: Column(
        children: [
          Text(
            progressText,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: theme.primaryText,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            height: 8,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              gradient: LinearGradient(
                colors: [
                  theme.progressGradient.first.withValues(alpha: 0.3),
                  theme.progressGradient.last.withValues(alpha: 0.3),
                ],
              ),
            ),
            child: FractionallySizedBox(
              alignment: Alignment.centerRight,
              widthFactor: progress / 100,
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  gradient: LinearGradient(
                    colors: theme.progressGradient,
                    begin: Alignment.centerRight,
                    end: Alignment.centerLeft,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuranGrid(theme, QuranProvider quranProvider, String section) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 6,
          crossAxisSpacing: 8,
          mainAxisSpacing: 8,
          childAspectRatio: 1,
        ),
        itemCount: 30, // 30 Juz
        itemBuilder: (context, index) {
          final juzNumber = index + 1;
          final juz = QuranJuz.getJuzByNumber(juzNumber);
          final progress = quranProvider.getJuzProgress(juzNumber);

          return GestureDetector(
            onTap:
                () =>
                    _showJuzPages(context, juz, quranProvider, theme, section),
            child: Container(
              decoration: BoxDecoration(
                color: theme.cardBackground,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: theme.borderColor),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    juzNumber.toString(),
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: theme.primaryText,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Container(
                    width: 30,
                    height: 4,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(2),
                      color: theme.progressGradient.first.withValues(
                        alpha: 0.3,
                      ),
                    ),
                    child: FractionallySizedBox(
                      alignment: Alignment.centerRight,
                      widthFactor: (progress[section] ?? 0) / 100,
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(2),
                          color: theme.progressGradient.first,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  void _showJuzPages(
    BuildContext context,
    QuranJuz juz,
    QuranProvider quranProvider,
    theme,
    String section,
  ) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder:
          (context) => Container(
            height: MediaQuery.of(context).size.height * 0.7,
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'الجزء ${juz.number} - ${juz.name}',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: theme.primaryText,
                  ),
                ),
                Text(
                  'من صفحة ${juz.startPage} إلى ${juz.endPage}',
                  style: TextStyle(color: theme.secondaryText),
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: GridView.builder(
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 8,
                          crossAxisSpacing: 4,
                          mainAxisSpacing: 4,
                          childAspectRatio: 1,
                        ),
                    itemCount: juz.totalPages,
                    itemBuilder: (context, index) {
                      final pageNumber = juz.startPage + index;
                      final page = quranProvider.pages.firstWhere(
                        (p) => p.pageNumber == pageNumber,
                        orElse:
                            () => QuranPage(
                              pageNumber: pageNumber,
                              juzNumber: juz.number,
                            ),
                      );

                      bool isCompleted = false;
                      switch (section) {
                        case 'recitation':
                          isCompleted = page.isRecited;
                          break;
                        case 'memorization':
                          isCompleted = page.isMemorized;
                          break;
                        case 'review':
                          isCompleted = page.isReviewed;
                          break;
                      }

                      return GestureDetector(
                        onTap:
                            () => _togglePageStatus(
                              quranProvider,
                              pageNumber,
                              section,
                              !isCompleted,
                            ),
                        child: Container(
                          decoration: BoxDecoration(
                            color:
                                isCompleted
                                    ? theme.progressGradient.first
                                    : theme.cardBackground,
                            borderRadius: BorderRadius.circular(4),
                            border: Border.all(color: theme.borderColor),
                          ),
                          child: Center(
                            child: Text(
                              pageNumber.toString(),
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color:
                                    isCompleted
                                        ? Colors.white
                                        : theme.primaryText,
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
    );
  }

  void _togglePageStatus(
    QuranProvider quranProvider,
    int pageNumber,
    String section,
    bool status,
  ) {
    quranProvider.updatePageStatus(pageNumber, section, status);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
}
