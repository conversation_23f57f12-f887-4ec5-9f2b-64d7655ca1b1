class Book {
  final String id;
  final String title;
  final String author;
  final int totalPages;
  final int currentPage;
  final String? coverImagePath;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final DateTime? completedAt;

  Book({
    required this.id,
    required this.title,
    required this.author,
    required this.totalPages,
    this.currentPage = 0,
    this.coverImagePath,
    this.isActive = true,
    required this.createdAt,
    this.updatedAt,
    this.completedAt,
  });

  double get progressPercentage {
    if (totalPages == 0) return 0.0;
    return (currentPage / totalPages) * 100;
  }

  bool get isCompleted => currentPage >= totalPages;

  int get remainingPages => totalPages - currentPage;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'author': author,
      'totalPages': totalPages,
      'currentPage': currentPage,
      'coverImagePath': coverImagePath,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
    };
  }

  factory Book.fromJson(Map<String, dynamic> json) {
    return Book(
      id: json['id'],
      title: json['title'],
      author: json['author'],
      totalPages: json['totalPages'],
      currentPage: json['currentPage'] ?? 0,
      coverImagePath: json['coverImagePath'],
      isActive: json['isActive'] ?? true,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
      completedAt: json['completedAt'] != null ? DateTime.parse(json['completedAt']) : null,
    );
  }

  Book copyWith({
    String? id,
    String? title,
    String? author,
    int? totalPages,
    int? currentPage,
    String? coverImagePath,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? completedAt,
  }) {
    return Book(
      id: id ?? this.id,
      title: title ?? this.title,
      author: author ?? this.author,
      totalPages: totalPages ?? this.totalPages,
      currentPage: currentPage ?? this.currentPage,
      coverImagePath: coverImagePath ?? this.coverImagePath,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      completedAt: completedAt ?? this.completedAt,
    );
  }
}

class ReadingSession {
  final String id;
  final String bookId;
  final int startPage;
  final int endPage;
  final DateTime date;
  final Duration readingTime;
  final DateTime createdAt;

  ReadingSession({
    required this.id,
    required this.bookId,
    required this.startPage,
    required this.endPage,
    required this.date,
    required this.readingTime,
    required this.createdAt,
  });

  int get pagesRead => endPage - startPage;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'bookId': bookId,
      'startPage': startPage,
      'endPage': endPage,
      'date': date.toIso8601String(),
      'readingTime': readingTime.inMinutes,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory ReadingSession.fromJson(Map<String, dynamic> json) {
    return ReadingSession(
      id: json['id'],
      bookId: json['bookId'],
      startPage: json['startPage'],
      endPage: json['endPage'],
      date: DateTime.parse(json['date']),
      readingTime: Duration(minutes: json['readingTime']),
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}
