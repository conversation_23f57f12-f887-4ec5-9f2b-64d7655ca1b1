import 'package:flutter/material.dart';

class AppTheme {
  final String id;
  final String name;
  final String category; // 'day' or 'night'
  final Color primaryBackground;
  final Color cardBackground;
  final Color primaryText;
  final Color secondaryText;
  final Color borderColor;
  final List<Color> progressGradient;

  const AppTheme({
    required this.id,
    required this.name,
    required this.category,
    required this.primaryBackground,
    required this.cardBackground,
    required this.primaryText,
    required this.secondaryText,
    required this.borderColor,
    required this.progressGradient,
  });

  ThemeData get themeData {
    return ThemeData(
      useMaterial3: true,
      brightness: category == 'night' ? Brightness.dark : Brightness.light,
      scaffoldBackgroundColor: primaryBackground,
      cardColor: cardBackground,
      textTheme: TextTheme(
        bodyLarge: TextStyle(color: primaryText),
        bodyMedium: TextStyle(color: primaryText),
        bodySmall: TextStyle(color: secondaryText),
        headlineLarge: TextStyle(
          color: primaryText,
          fontWeight: FontWeight.bold,
        ),
        headlineMedium: TextStyle(
          color: primaryText,
          fontWeight: FontWeight.bold,
        ),
        headlineSmall: TextStyle(
          color: primaryText,
          fontWeight: FontWeight.bold,
        ),
        titleLarge: TextStyle(color: primaryText, fontWeight: FontWeight.w600),
        titleMedium: TextStyle(color: primaryText, fontWeight: FontWeight.w600),
        titleSmall: TextStyle(color: primaryText, fontWeight: FontWeight.w600),
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: primaryBackground,
        foregroundColor: primaryText,
        elevation: 0,
      ),
      cardTheme: CardTheme(
        color: cardBackground,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(color: borderColor, width: 1),
        ),
      ),
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: cardBackground,
        selectedItemColor: progressGradient.first,
        unselectedItemColor: secondaryText,
        type: BottomNavigationBarType.fixed,
      ),
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: progressGradient.first,
        foregroundColor: category == 'night' ? Colors.white : Colors.black,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: progressGradient.first,
          foregroundColor: category == 'night' ? Colors.white : Colors.black,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: cardBackground,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: borderColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: borderColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: progressGradient.first, width: 2),
        ),
      ),
    );
  }
}

class AppThemes {
  // الهويات النهارية
  static const List<AppTheme> dayThemes = [
    AppTheme(
      id: 'jasmine_flowers',
      name: 'زهور الياسمين',
      category: 'day',
      primaryBackground: Color(0xFFFDF9F5),
      cardBackground: Color(0xFFFFFFFF),
      primaryText: Color(0xFF2D4A3E),
      secondaryText: Color(0xFF7B9B85),
      borderColor: Color(0xFFE8D5B7),
      progressGradient: [Color(0xFF2D4A3E), Color(0xFF7B9B85)],
    ),
    AppTheme(
      id: 'morning_light',
      name: 'نور الصباح',
      category: 'day',
      primaryBackground: Color(0xFFFFFFFF),
      cardBackground: Color(0xFFF5F5F5),
      primaryText: Color(0xFF000000),
      secondaryText: Color(0xFF555555),
      borderColor: Color(0xFFCCCCCC),
      progressGradient: [Color(0xFF000000), Color(0xFF555555)],
    ),
    AppTheme(
      id: 'andalus_mosaic',
      name: 'فسيفساء الأندلس',
      category: 'day',
      primaryBackground: Color(0xFFF5F1E8),
      cardBackground: Color(0xFFFFFFFF),
      primaryText: Color(0xFF1C3D5A),
      secondaryText: Color(0xFF4BB1A0),
      borderColor: Color(0xFFD4AF37),
      progressGradient: [Color(0xFF1C3D5A), Color(0xFF4BB1A0)],
    ),
    AppTheme(
      id: 'palm_fragrance',
      name: 'أريج النخيل',
      category: 'day',
      primaryBackground: Color(0xFFFAF5ED),
      cardBackground: Color(0xFFFFFFFF),
      primaryText: Color(0xFF60503D),
      secondaryText: Color(0xFF7A9E7E),
      borderColor: Color(0xFFD9C47E),
      progressGradient: [Color(0xFF60503D), Color(0xFF7A9E7E)],
    ),
    AppTheme(
      id: 'andalus_nights',
      name: 'ليالي الأندلس',
      category: 'day',
      primaryBackground: Color(0xFFECECEC),
      cardBackground: Color(0xFFFFFFFF),
      primaryText: Color(0xFF0F1E3D),
      secondaryText: Color(0xFF6D5A7A),
      borderColor: Color(0xFFB8B8B8),
      progressGradient: [Color(0xFF0F1E3D), Color(0xFF6D5A7A)],
    ),
    AppTheme(
      id: 'beach_sand',
      name: 'رمال الشاطئ',
      category: 'day',
      primaryBackground: Color(0xFFFBF6F0),
      cardBackground: Color(0xFFFFFFFF),
      primaryText: Color(0xFF4A5568),
      secondaryText: Color(0xFF6B8DB5),
      borderColor: Color(0xFFD1C4B3),
      progressGradient: [Color(0xFF4A5568), Color(0xFF6B8DB5)],
    ),
  ];

  // المزيد من الهويات النهارية
  static const List<AppTheme> dayThemes2 = [
    AppTheme(
      id: 'earth_soil',
      name: 'تربة الأرض',
      category: 'day',
      primaryBackground: Color(0xFFF8F5F1),
      cardBackground: Color(0xFFFFFFFF),
      primaryText: Color(0xFF5D4E37),
      secondaryText: Color(0xFF8B7355),
      borderColor: Color(0xFFC7B299),
      progressGradient: [Color(0xFF5D4E37), Color(0xFF8B7355)],
    ),
    AppTheme(
      id: 'gulf_waves',
      name: 'موج الخليج',
      category: 'day',
      primaryBackground: Color(0xFFFFF8E1),
      cardBackground: Color(0xFFFFFBFA),
      primaryText: Color(0xFF1565C0),
      secondaryText: Color(0xFF26A69A),
      borderColor: Color(0xFF90CAF9),
      progressGradient: [Color(0xFF1565C0), Color(0xFF26A69A)],
    ),
    AppTheme(
      id: 'autumn_leaves',
      name: 'أوراق الخريف',
      category: 'day',
      primaryBackground: Color(0xFFFFF3E0),
      cardBackground: Color(0xFFFFF8E1),
      primaryText: Color(0xFF795548),
      secondaryText: Color(0xFFD32F2F),
      borderColor: Color(0xFFFFD700),
      progressGradient: [Color(0xFF795548), Color(0xFFD32F2F)],
    ),
    AppTheme(
      id: 'art_palette',
      name: 'لوحة فنية',
      category: 'day',
      primaryBackground: Color(0xFFBBDEFB),
      cardBackground: Color(0xFFFFFFFF),
      primaryText: Color(0xFF000000),
      secondaryText: Color(0xFF424242),
      borderColor: Color(0xFFC8E6C9),
      progressGradient: [Color(0xFF000000), Color(0xFF424242)],
    ),
    AppTheme(
      id: 'sky_clarity',
      name: 'صفاء السماء',
      category: 'day',
      primaryBackground: Color(0xFFF0F8FF),
      cardBackground: Color(0xFFFFFAFA),
      primaryText: Color(0xFF4169E1),
      secondaryText: Color(0xFF4682B4),
      borderColor: Color(0xFF87CEEB),
      progressGradient: [Color(0xFF4169E1), Color(0xFF4682B4)],
    ),
    AppTheme(
      id: 'gray_breeze',
      name: 'النسيم الرمادي',
      category: 'day',
      primaryBackground: Color(0xFFF8F8FF),
      cardBackground: Color(0xFFF0F8FF),
      primaryText: Color(0xFF708090),
      secondaryText: Color(0xFF778899),
      borderColor: Color(0xFFC0C0C0),
      progressGradient: [Color(0xFF708090), Color(0xFF778899)],
    ),
  ];

  // الهويات المسائية
  static const List<AppTheme> nightThemes = [
    AppTheme(
      id: 'dark_caves',
      name: 'الكهوف الدامسة',
      category: 'night',
      primaryBackground: Color(0xFF1A1A1A),
      cardBackground: Color(0xFF2A2A2A),
      primaryText: Color(0xFFE5E5E5),
      secondaryText: Color(0xFFA0A0A0),
      borderColor: Color(0xFF404040),
      progressGradient: [Color(0xFFE5E5E5), Color(0xFFA0A0A0)],
    ),
    AppTheme(
      id: 'moonlight',
      name: 'ضوء القمر',
      category: 'night',
      primaryBackground: Color(0xFF0D1117),
      cardBackground: Color(0xFF1A1F26),
      primaryText: Color(0xFFE6E8EA),
      secondaryText: Color(0xFFBFC7D1),
      borderColor: Color(0xFF2C303A),
      progressGradient: [Color(0xFFE6E8EA), Color(0xFFBFC7D1)],
    ),
    AppTheme(
      id: 'night_shadows',
      name: 'ظلال الليل',
      category: 'night',
      primaryBackground: Color(0xFF000000),
      cardBackground: Color(0xFF333333),
      primaryText: Color(0xFFFFFFFF),
      secondaryText: Color(0xFFCCCCCC),
      borderColor: Color(0xFF666666),
      progressGradient: [Color(0xFFFFFFFF), Color(0xFFCCCCCC)],
    ),
    AppTheme(
      id: 'deep_night',
      name: 'الليل العميق',
      category: 'night',
      primaryBackground: Color(0xFF222222),
      cardBackground: Color(0xFF444444),
      primaryText: Color(0xFFFFFFFF),
      secondaryText: Color(0xFFDDDDDD),
      borderColor: Color(0xFF888888),
      progressGradient: [Color(0xFFFFFFFF), Color(0xFFDDDDDD)],
    ),
    AppTheme(
      id: 'rock_depth',
      name: 'عمق الصخر',
      category: 'night',
      primaryBackground: Color(0xFF2A1F1A),
      cardBackground: Color(0xFF3A2D26),
      primaryText: Color(0xFFE8D5B7),
      secondaryText: Color(0xFFD4A574),
      borderColor: Color(0xFF5D4A3A),
      progressGradient: [Color(0xFFE8D5B7), Color(0xFFD4A574)],
    ),
    AppTheme(
      id: 'purple_night',
      name: 'الليل الأرجواني',
      category: 'night',
      primaryBackground: Color(0xFF1D1B2A),
      cardBackground: Color(0xFF2D2A3A),
      primaryText: Color(0xFFE0DCF0),
      secondaryText: Color(0xFF9B85C7),
      borderColor: Color(0xFF44405A),
      progressGradient: [Color(0xFFE0DCF0), Color(0xFF9B85C7)],
    ),
  ];

  // المزيد من الهويات المسائية
  static const List<AppTheme> nightThemes2 = [
    AppTheme(
      id: 'desert_sand',
      name: 'رمال الصحراء',
      category: 'night',
      primaryBackground: Color(0xFF1F1B0F),
      cardBackground: Color(0xFF2F2A1A),
      primaryText: Color(0xFFF5E6A3),
      secondaryText: Color(0xFFD2B48C),
      borderColor: Color(0xFF4A3F2A),
      progressGradient: [Color(0xFFF5E6A3), Color(0xFFD2B48C)],
    ),
    AppTheme(
      id: 'sunset_twilight',
      name: 'شفق الغروب',
      category: 'night',
      primaryBackground: Color(0xFF1A132A),
      cardBackground: Color(0xFF2A1F3A),
      primaryText: Color(0xFFE6D4B8),
      secondaryText: Color(0xFFCC7A4B),
      borderColor: Color(0xFF7A7D85),
      progressGradient: [Color(0xFFE6D4B8), Color(0xFFCC7A4B)],
    ),
    AppTheme(
      id: 'night_silence',
      name: 'صمت الليل',
      category: 'night',
      primaryBackground: Color(0xFF212121),
      cardBackground: Color(0xFF1B1B1B),
      primaryText: Color(0xFFFFFFFF),
      secondaryText: Color(0xFF9E9E9E),
      borderColor: Color(0xFF424242),
      progressGradient: [Color(0xFFFFFFFF), Color(0xFF9E9E9E)],
    ),
    AppTheme(
      id: 'tranquil_hills',
      name: 'ربوع السكون',
      category: 'night',
      primaryBackground: Color(0xFF2E3B4E),
      cardBackground: Color(0xFF1F2A38),
      primaryText: Color(0xFFECEFF1),
      secondaryText: Color(0xFF81C784),
      borderColor: Color(0xFF607D8B),
      progressGradient: [Color(0xFFECEFF1), Color(0xFF81C784)],
    ),
    AppTheme(
      id: 'moonlit_night',
      name: 'ليلة مقمرة',
      category: 'night',
      primaryBackground: Color(0xFF0A192F),
      cardBackground: Color(0xFF2C3E50),
      primaryText: Color(0xFFFFFFFF),
      secondaryText: Color(0xFFBDC3C7),
      borderColor: Color(0xFF3498DB),
      progressGradient: [Color(0xFFFFFFFF), Color(0xFFBDC3C7)],
    ),
    AppTheme(
      id: 'rocky_soil',
      name: 'التربة الصخرية',
      category: 'night',
      primaryBackground: Color(0xFF1F1F0F),
      cardBackground: Color(0xFF2F2F1F),
      primaryText: Color(0xFFF5F5DC),
      secondaryText: Color(0xFFF5DEB3),
      borderColor: Color(0xFFD2B48C),
      progressGradient: [Color(0xFFF5F5DC), Color(0xFFF5DEB3)],
    ),
  ];

  static List<AppTheme> get allThemes {
    return [...dayThemes, ...dayThemes2, ...nightThemes, ...nightThemes2];
  }

  static List<AppTheme> get dayThemesList {
    return [...dayThemes, ...dayThemes2];
  }

  static List<AppTheme> get nightThemesList {
    return [...nightThemes, ...nightThemes2];
  }

  static AppTheme getThemeById(String id) {
    return allThemes.firstWhere(
      (theme) => theme.id == id,
      orElse: () => dayThemes.first,
    );
  }
}
