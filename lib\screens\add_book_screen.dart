import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../providers/theme_provider.dart';
import '../providers/book_provider.dart';
import '../models/book.dart';

class AddBookScreen extends StatefulWidget {
  final Book? book; // For editing existing book

  const AddBookScreen({super.key, this.book});

  @override
  State<AddBookScreen> createState() => _AddBookScreenState();
}

class _AddBookScreenState extends State<AddBookScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _authorController = TextEditingController();
  final _totalPagesController = TextEditingController();
  final _currentPageController = TextEditingController();

  String? _coverImagePath;
  final ImagePicker _imagePicker = ImagePicker();

  @override
  void initState() {
    super.initState();
    if (widget.book != null) {
      _loadBookData();
    }
  }

  void _loadBookData() {
    final book = widget.book!;
    _titleController.text = book.title;
    _authorController.text = book.author;
    _totalPagesController.text = book.totalPages.toString();
    _currentPageController.text = book.currentPage.toString();
    _coverImagePath = book.coverImagePath;
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<ThemeProvider, BookProvider>(
      builder: (context, themeProvider, bookProvider, child) {
        final theme = themeProvider.currentTheme;
        
        return Scaffold(
          appBar: AppBar(
            title: Text(
              widget.book != null ? 'تعديل الكتاب' : 'إضافة كتاب جديد',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: theme.primaryText,
              ),
            ),
            backgroundColor: theme.primaryBackground,
            elevation: 0,
            actions: [
              TextButton(
                onPressed: _saveBook,
                child: Text(
                  'حفظ',
                  style: TextStyle(
                    color: theme.progressGradient.first,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          body: Form(
            key: _formKey,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildCoverImageSection(theme),
                  const SizedBox(height: 24),
                  _buildTitleField(theme),
                  const SizedBox(height: 16),
                  _buildAuthorField(theme),
                  const SizedBox(height: 16),
                  _buildTotalPagesField(theme),
                  const SizedBox(height: 16),
                  if (widget.book != null) ...[
                    _buildCurrentPageField(theme),
                    const SizedBox(height: 16),
                  ],
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildCoverImageSection(theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'صورة الغلاف',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: theme.primaryText,
          ),
        ),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: _pickImage,
          child: Container(
            width: 120,
            height: 160,
            decoration: BoxDecoration(
              color: theme.cardBackground,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: theme.borderColor),
            ),
            child: _coverImagePath != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.file(
                      File(_coverImagePath!),
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return _buildImagePlaceholder(theme);
                      },
                    ),
                  )
                : _buildImagePlaceholder(theme),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'اضغط لاختيار صورة الغلاف (اختياري)',
          style: TextStyle(
            fontSize: 12,
            color: theme.secondaryText,
          ),
        ),
      ],
    );
  }

  Widget _buildImagePlaceholder(theme) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.book,
          size: 40,
          color: theme.secondaryText,
        ),
        const SizedBox(height: 8),
        Text(
          'إضافة صورة',
          style: TextStyle(
            fontSize: 12,
            color: theme.secondaryText,
          ),
        ),
      ],
    );
  }

  Widget _buildTitleField(theme) {
    return TextFormField(
      controller: _titleController,
      decoration: InputDecoration(
        labelText: 'عنوان الكتاب',
        hintText: 'مثال: الأسود يليق بك',
        labelStyle: TextStyle(color: theme.primaryText),
        hintStyle: TextStyle(color: theme.secondaryText),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'يرجى إدخال عنوان الكتاب';
        }
        return null;
      },
    );
  }

  Widget _buildAuthorField(theme) {
    return TextFormField(
      controller: _authorController,
      decoration: InputDecoration(
        labelText: 'المؤلف',
        hintText: 'مثال: أحمد محمد',
        labelStyle: TextStyle(color: theme.primaryText),
        hintStyle: TextStyle(color: theme.secondaryText),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'يرجى إدخال اسم المؤلف';
        }
        return null;
      },
    );
  }

  Widget _buildTotalPagesField(theme) {
    return TextFormField(
      controller: _totalPagesController,
      keyboardType: TextInputType.number,
      decoration: InputDecoration(
        labelText: 'عدد الصفحات',
        hintText: 'مثال: 300',
        labelStyle: TextStyle(color: theme.primaryText),
        hintStyle: TextStyle(color: theme.secondaryText),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'يرجى إدخال عدد الصفحات';
        }
        final pages = int.tryParse(value);
        if (pages == null || pages <= 0) {
          return 'يرجى إدخال رقم صحيح أكبر من صفر';
        }
        return null;
      },
    );
  }

  Widget _buildCurrentPageField(theme) {
    return TextFormField(
      controller: _currentPageController,
      keyboardType: TextInputType.number,
      decoration: InputDecoration(
        labelText: 'الصفحة الحالية',
        hintText: 'مثال: 150',
        labelStyle: TextStyle(color: theme.primaryText),
        hintStyle: TextStyle(color: theme.secondaryText),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'يرجى إدخال الصفحة الحالية';
        }
        final currentPage = int.tryParse(value);
        final totalPages = int.tryParse(_totalPagesController.text);
        
        if (currentPage == null || currentPage < 0) {
          return 'يرجى إدخال رقم صحيح';
        }
        
        if (totalPages != null && currentPage > totalPages) {
          return 'الصفحة الحالية لا يمكن أن تكون أكبر من العدد الكلي';
        }
        
        return null;
      },
    );
  }

  Future<void> _pickImage() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 800,
        maxHeight: 1200,
        imageQuality: 80,
      );
      
      if (image != null) {
        setState(() {
          _coverImagePath = image.path;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('حدث خطأ أثناء اختيار الصورة'),
          ),
        );
      }
    }
  }

  void _saveBook() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final bookProvider = Provider.of<BookProvider>(context, listen: false);
    
    final book = Book(
      id: widget.book?.id ?? DateTime.now().millisecondsSinceEpoch.toString(),
      title: _titleController.text.trim(),
      author: _authorController.text.trim(),
      totalPages: int.parse(_totalPagesController.text),
      currentPage: widget.book != null 
        ? int.parse(_currentPageController.text)
        : 0,
      coverImagePath: _coverImagePath,
      createdAt: widget.book?.createdAt ?? DateTime.now(),
      updatedAt: DateTime.now(),
    );

    try {
      if (widget.book != null) {
        await bookProvider.updateBook(book);
      } else {
        await bookProvider.addBook(book);
      }
      
      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.book != null 
                ? 'تم تحديث الكتاب بنجاح' 
                : 'تم إضافة الكتاب بنجاح',
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('حدث خطأ أثناء حفظ الكتاب'),
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _authorController.dispose();
    _totalPagesController.dispose();
    _currentPageController.dispose();
    super.dispose();
  }
}
