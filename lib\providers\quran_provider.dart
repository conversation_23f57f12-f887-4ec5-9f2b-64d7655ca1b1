import 'package:flutter/material.dart';
import '../models/quran.dart';
import '../services/database_service.dart';

class <PERSON><PERSON>rovider extends ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();
  List<QuranPage> _pages = [];
  List<QuranPlan> _plans = [];
  bool _isLoading = false;

  List<QuranPage> get pages => _pages;
  List<QuranPlan> get plans => _plans;
  bool get isLoading => _isLoading;

  QuranProvider() {
    loadPages();
    loadPlans();
  }

  Future<void> loadPages() async {
    _isLoading = true;
    notifyListeners();

    try {
      _pages = await _databaseService.getQuranPages();
    } catch (e) {
      debugPrint('Error loading Quran pages: $e');
    }

    _isLoading = false;
    notifyListeners();
  }

  Future<void> loadPlans() async {
    try {
      _plans = await _databaseService.getQuranPlans();
    } catch (e) {
      debugPrint('Error loading Quran plans: $e');
    }
    notifyListeners();
  }

  Future<void> updatePageStatus(int pageNumber, String type, bool status) async {
    try {
      final pageIndex = _pages.indexWhere((p) => p.pageNumber == pageNumber);
      if (pageIndex != -1) {
        QuranPage updatedPage;
        final now = DateTime.now();
        
        switch (type) {
          case 'recitation':
            updatedPage = _pages[pageIndex].copyWith(
              isRecited: status,
              recitedDate: status ? now : null,
            );
            break;
          case 'memorization':
            updatedPage = _pages[pageIndex].copyWith(
              isMemorized: status,
              memorizedDate: status ? now : null,
              // If unmarking memorization, also unmark review
              isReviewed: status ? _pages[pageIndex].isReviewed : false,
              reviewedDate: status ? _pages[pageIndex].reviewedDate : null,
            );
            break;
          case 'review':
            // Can only review if already memorized
            if (_pages[pageIndex].isMemorized) {
              updatedPage = _pages[pageIndex].copyWith(
                isReviewed: status,
                reviewedDate: status ? now : null,
              );
            } else {
              return; // Can't review if not memorized
            }
            break;
          default:
            return;
        }
        
        await _databaseService.updateQuranPage(updatedPage);
        _pages[pageIndex] = updatedPage;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error updating page status: $e');
    }
  }

  Future<void> addPlan(QuranPlan plan) async {
    try {
      await _databaseService.insertQuranPlan(plan);
      _plans.add(plan);
      notifyListeners();
    } catch (e) {
      debugPrint('Error adding plan: $e');
    }
  }

  Future<void> updatePlan(QuranPlan plan) async {
    try {
      await _databaseService.updateQuranPlan(plan);
      final index = _plans.indexWhere((p) => p.id == plan.id);
      if (index != -1) {
        _plans[index] = plan;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error updating plan: $e');
    }
  }

  Future<void> deletePlan(String planId) async {
    try {
      await _databaseService.deleteQuranPlan(planId);
      _plans.removeWhere((p) => p.id == planId);
      notifyListeners();
    } catch (e) {
      debugPrint('Error deleting plan: $e');
    }
  }

  // Statistics methods
  int getRecitedPagesCount() {
    return _pages.where((p) => p.isRecited).length;
  }

  int getMemorizedPagesCount() {
    return _pages.where((p) => p.isMemorized).length;
  }

  int getReviewedPagesCount() {
    return _pages.where((p) => p.isReviewed).length;
  }

  double getRecitationProgress() {
    return (getRecitedPagesCount() / 604) * 100;
  }

  double getMemorizationProgress() {
    return (getMemorizedPagesCount() / 604) * 100;
  }

  double getReviewProgress() {
    final memorizedCount = getMemorizedPagesCount();
    if (memorizedCount == 0) return 0.0;
    return (getReviewedPagesCount() / memorizedCount) * 100;
  }

  List<QuranPlan> getActivePlans() {
    return _plans.where((plan) => plan.isActive).toList();
  }

  QuranPlan? getActivePlanByType(String type) {
    return _plans.where((plan) => plan.type == type && plan.isActive).firstOrNull;
  }

  // Get today's target for each plan type
  Map<String, int> getTodayTargets() {
    final targets = <String, int>{};
    final activePlans = getActivePlans();
    
    for (final plan in activePlans) {
      targets[plan.type] = plan.dailyTarget;
    }
    
    return targets;
  }

  // Get pages by Juz
  List<QuranPage> getPagesByJuz(int juzNumber) {
    return _pages.where((p) => p.juzNumber == juzNumber).toList();
  }

  // Get Juz progress
  Map<String, double> getJuzProgress(int juzNumber) {
    final juzPages = getPagesByJuz(juzNumber);
    final totalPages = juzPages.length;
    
    if (totalPages == 0) {
      return {'recitation': 0.0, 'memorization': 0.0, 'review': 0.0};
    }
    
    final recitedCount = juzPages.where((p) => p.isRecited).length;
    final memorizedCount = juzPages.where((p) => p.isMemorized).length;
    final reviewedCount = juzPages.where((p) => p.isReviewed).length;
    
    return {
      'recitation': (recitedCount / totalPages) * 100,
      'memorization': (memorizedCount / totalPages) * 100,
      'review': memorizedCount > 0 ? (reviewedCount / memorizedCount) * 100 : 0.0,
    };
  }
}
