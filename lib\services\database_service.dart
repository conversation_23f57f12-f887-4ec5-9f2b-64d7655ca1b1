import 'dart:async';
import 'dart:io';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import '../models/routine.dart';
import '../models/book.dart';
import '../models/quran.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    String path = join(documentsDirectory.path, 'injaz_app.db');
    
    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Routines table
    await db.execute('''
      CREATE TABLE routines(
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        category TEXT NOT NULL,
        measureType TEXT NOT NULL,
        targetValue REAL NOT NULL,
        unit TEXT NOT NULL,
        frequency TEXT NOT NULL,
        selectedDays TEXT NOT NULL,
        isActive INTEGER NOT NULL DEFAULT 1,
        createdAt TEXT NOT NULL,
        updatedAt TEXT
      )
    ''');

    // Routine Progress table
    await db.execute('''
      CREATE TABLE routine_progress(
        id TEXT PRIMARY KEY,
        routineId TEXT NOT NULL,
        date TEXT NOT NULL,
        currentValue REAL NOT NULL,
        isCompleted INTEGER NOT NULL DEFAULT 0,
        createdAt TEXT NOT NULL,
        updatedAt TEXT,
        FOREIGN KEY (routineId) REFERENCES routines (id) ON DELETE CASCADE
      )
    ''');

    // Books table
    await db.execute('''
      CREATE TABLE books(
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        author TEXT NOT NULL,
        totalPages INTEGER NOT NULL,
        currentPage INTEGER NOT NULL DEFAULT 0,
        coverImagePath TEXT,
        isActive INTEGER NOT NULL DEFAULT 1,
        createdAt TEXT NOT NULL,
        updatedAt TEXT,
        completedAt TEXT
      )
    ''');

    // Reading Sessions table
    await db.execute('''
      CREATE TABLE reading_sessions(
        id TEXT PRIMARY KEY,
        bookId TEXT NOT NULL,
        startPage INTEGER NOT NULL,
        endPage INTEGER NOT NULL,
        date TEXT NOT NULL,
        readingTime INTEGER NOT NULL,
        createdAt TEXT NOT NULL,
        FOREIGN KEY (bookId) REFERENCES books (id) ON DELETE CASCADE
      )
    ''');

    // Quran Pages table
    await db.execute('''
      CREATE TABLE quran_pages(
        pageNumber INTEGER PRIMARY KEY,
        juzNumber INTEGER NOT NULL,
        isRecited INTEGER NOT NULL DEFAULT 0,
        isMemorized INTEGER NOT NULL DEFAULT 0,
        isReviewed INTEGER NOT NULL DEFAULT 0,
        recitedDate TEXT,
        memorizedDate TEXT,
        reviewedDate TEXT
      )
    ''');

    // Quran Plans table
    await db.execute('''
      CREATE TABLE quran_plans(
        id TEXT PRIMARY KEY,
        type TEXT NOT NULL,
        dailyTarget INTEGER NOT NULL,
        isActive INTEGER NOT NULL DEFAULT 1,
        createdAt TEXT NOT NULL,
        updatedAt TEXT
      )
    ''');

    // Initialize Quran pages (1-604)
    for (int i = 1; i <= 604; i++) {
      int juzNumber = _getJuzNumber(i);
      await db.insert('quran_pages', {
        'pageNumber': i,
        'juzNumber': juzNumber,
        'isRecited': 0,
        'isMemorized': 0,
        'isReviewed': 0,
      });
    }
  }

  int _getJuzNumber(int pageNumber) {
    if (pageNumber <= 22) return 1;
    if (pageNumber >= 583) return 30;
    return ((pageNumber - 23) ~/ 20) + 2;
  }

  // Routine methods
  Future<List<Routine>> getRoutines() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('routines');
    
    return List.generate(maps.length, (i) {
      final map = Map<String, dynamic>.from(maps[i]);
      map['selectedDays'] = (map['selectedDays'] as String)
          .split(',')
          .map((e) => int.parse(e))
          .toList();
      return Routine.fromJson(map);
    });
  }

  Future<void> insertRoutine(Routine routine) async {
    final db = await database;
    final map = routine.toJson();
    map['selectedDays'] = routine.selectedDays.join(',');
    await db.insert('routines', map);
  }

  Future<void> updateRoutine(Routine routine) async {
    final db = await database;
    final map = routine.toJson();
    map['selectedDays'] = routine.selectedDays.join(',');
    await db.update(
      'routines',
      map,
      where: 'id = ?',
      whereArgs: [routine.id],
    );
  }

  Future<void> deleteRoutine(String id) async {
    final db = await database;
    await db.delete(
      'routines',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Routine Progress methods
  Future<List<RoutineProgress>> getRoutineProgress(String routineId, DateTime date) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'routine_progress',
      where: 'routineId = ? AND date = ?',
      whereArgs: [routineId, date.toIso8601String().split('T')[0]],
    );
    
    return List.generate(maps.length, (i) {
      return RoutineProgress.fromJson(maps[i]);
    });
  }

  Future<void> insertRoutineProgress(RoutineProgress progress) async {
    final db = await database;
    await db.insert('routine_progress', progress.toJson());
  }

  Future<void> updateRoutineProgress(RoutineProgress progress) async {
    final db = await database;
    await db.update(
      'routine_progress',
      progress.toJson(),
      where: 'id = ?',
      whereArgs: [progress.id],
    );
  }

  // Book methods
  Future<List<Book>> getBooks() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('books');
    
    return List.generate(maps.length, (i) {
      return Book.fromJson(maps[i]);
    });
  }

  Future<void> insertBook(Book book) async {
    final db = await database;
    await db.insert('books', book.toJson());
  }

  Future<void> updateBook(Book book) async {
    final db = await database;
    await db.update(
      'books',
      book.toJson(),
      where: 'id = ?',
      whereArgs: [book.id],
    );
  }

  Future<void> deleteBook(String id) async {
    final db = await database;
    await db.delete(
      'books',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Quran methods
  Future<List<QuranPage>> getQuranPages() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('quran_pages');
    
    return List.generate(maps.length, (i) {
      return QuranPage.fromJson(maps[i]);
    });
  }

  Future<void> updateQuranPage(QuranPage page) async {
    final db = await database;
    await db.update(
      'quran_pages',
      page.toJson(),
      where: 'pageNumber = ?',
      whereArgs: [page.pageNumber],
    );
  }

  Future<List<QuranPlan>> getQuranPlans() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('quran_plans');
    
    return List.generate(maps.length, (i) {
      return QuranPlan.fromJson(maps[i]);
    });
  }

  Future<void> insertQuranPlan(QuranPlan plan) async {
    final db = await database;
    await db.insert('quran_plans', plan.toJson());
  }

  Future<void> updateQuranPlan(QuranPlan plan) async {
    final db = await database;
    await db.update(
      'quran_plans',
      plan.toJson(),
      where: 'id = ?',
      whereArgs: [plan.id],
    );
  }

  Future<void> deleteQuranPlan(String id) async {
    final db = await database;
    await db.delete(
      'quran_plans',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Utility methods
  Future<void> clearAllData() async {
    final db = await database;
    await db.delete('routines');
    await db.delete('routine_progress');
    await db.delete('books');
    await db.delete('reading_sessions');
    await db.delete('quran_plans');
    
    // Reset Quran pages
    await db.update('quran_pages', {
      'isRecited': 0,
      'isMemorized': 0,
      'isReviewed': 0,
      'recitedDate': null,
      'memorizedDate': null,
      'reviewedDate': null,
    });
  }

  Future<void> close() async {
    final db = await database;
    await db.close();
  }
}
