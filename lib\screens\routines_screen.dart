import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';
import '../providers/routine_provider.dart';
import '../models/routine.dart';

import 'add_routine_screen.dart';

class RoutinesScreen extends StatelessWidget {
  const RoutinesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer2<ThemeProvider, RoutineProvider>(
      builder: (context, themeProvider, routineProvider, child) {
        final theme = themeProvider.currentTheme;
        final routines = routineProvider.routines;

        return Scaffold(
          appBar: AppBar(
            title: Text(
              'روتيني',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: theme.primaryText,
              ),
            ),
            backgroundColor: theme.primaryBackground,
            elevation: 0,
          ),
          body:
              routines.isEmpty
                  ? _buildEmptyState(theme)
                  : _buildRoutinesList(
                    context,
                    theme,
                    routines,
                    routineProvider,
                  ),
          floatingActionButton: FloatingActionButton(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const AddRoutineScreen(),
                ),
              );
            },
            child: const Icon(Icons.add),
          ),
        );
      },
    );
  }

  Widget _buildEmptyState(theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.repeat, size: 80, color: theme.secondaryText),
          const SizedBox(height: 16),
          Text(
            'لا توجد روتينات بعد',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: theme.primaryText,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ابدأ بإضافة روتين جديد لتتبع عاداتك اليومية',
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 16, color: theme.secondaryText),
          ),
        ],
      ),
    );
  }

  Widget _buildRoutinesList(
    BuildContext context,
    theme,
    List<Routine> routines,
    RoutineProvider routineProvider,
  ) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: routines.length,
      itemBuilder: (context, index) {
        final routine = routines[index];
        final progress = routineProvider.getRoutineProgress(routine.id);

        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: _buildRoutineIcon(routine, theme),
            title: Text(
              routine.name,
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: theme.primaryText,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getRoutineDescription(routine),
                  style: TextStyle(color: theme.secondaryText),
                ),
                const SizedBox(height: 4),
                Text(
                  _getFrequencyDescription(routine),
                  style: TextStyle(color: theme.secondaryText, fontSize: 12),
                ),
              ],
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (progress != null && progress.isCompleted)
                  Icon(Icons.check_circle, color: Colors.green, size: 20),
                PopupMenuButton<String>(
                  onSelected: (value) {
                    switch (value) {
                      case 'edit':
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder:
                                (context) => AddRoutineScreen(routine: routine),
                          ),
                        );
                        break;
                      case 'delete':
                        _showDeleteDialog(context, routine, routineProvider);
                        break;
                    }
                  },
                  itemBuilder:
                      (context) => [
                        const PopupMenuItem(
                          value: 'edit',
                          child: Text('تعديل'),
                        ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: Text('حذف'),
                        ),
                      ],
                ),
              ],
            ),
            onTap: () {
              _showRoutineDetails(context, routine, routineProvider, theme);
            },
          ),
        );
      },
    );
  }

  Widget _buildRoutineIcon(Routine routine, theme) {
    IconData icon;
    switch (routine.category) {
      case 'صحي':
        icon = Icons.favorite;
        break;
      case 'تعليمي':
        icon = Icons.school;
        break;
      case 'ديني':
        icon = Icons.mosque;
        break;
      case 'عادة':
        icon = Icons.star;
        break;
      default:
        icon = Icons.repeat;
    }

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: theme.progressGradient.first.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(icon, color: theme.progressGradient.first),
    );
  }

  String _getRoutineDescription(Routine routine) {
    if (routine.measureType == 'checkbox') {
      return 'مهمة يومية';
    } else {
      return '${routine.targetValue.toInt()} ${routine.unit}';
    }
  }

  String _getFrequencyDescription(Routine routine) {
    if (routine.frequency == 'daily') {
      return 'يومياً';
    } else {
      final dayNames = [
        'الاثنين',
        'الثلاثاء',
        'الأربعاء',
        'الخميس',
        'الجمعة',
        'السبت',
        'الأحد',
      ];
      final selectedDayNames = routine.selectedDays
          .map((day) => dayNames[day - 1])
          .join('، ');
      return selectedDayNames;
    }
  }

  void _showDeleteDialog(
    BuildContext context,
    Routine routine,
    RoutineProvider routineProvider,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('حذف الروتين'),
            content: Text('هل أنت متأكد من حذف روتين "${routine.name}"؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () async {
                  await routineProvider.deleteRoutine(routine.id);
                  if (context.mounted) {
                    Navigator.of(context).pop();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('تم حذف الروتين بنجاح')),
                    );
                  }
                },
                child: const Text('حذف'),
              ),
            ],
          ),
    );
  }

  void _showRoutineDetails(
    BuildContext context,
    Routine routine,
    RoutineProvider routineProvider,
    theme,
  ) {
    showModalBottomSheet(
      context: context,
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  routine.name,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: theme.primaryText,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'الفئة: ${routine.category}',
                  style: TextStyle(color: theme.secondaryText),
                ),
                Text(
                  'النوع: ${routine.measureType == 'checkbox'
                      ? 'مهمة'
                      : routine.measureType == 'counter'
                      ? 'عداد'
                      : 'مؤقت'}',
                  style: TextStyle(color: theme.secondaryText),
                ),
                if (routine.measureType != 'checkbox')
                  Text(
                    'الهدف: ${routine.targetValue.toInt()} ${routine.unit}',
                    style: TextStyle(color: theme.secondaryText),
                  ),
                Text(
                  'التكرار: ${_getFrequencyDescription(routine)}',
                  style: TextStyle(color: theme.secondaryText),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                          // TODO: Mark as completed for today
                        },
                        child: const Text('تسجيل الإنجاز'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
    );
  }
}
