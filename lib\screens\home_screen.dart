import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';
import '../providers/routine_provider.dart';
import '../providers/book_provider.dart';
import '../providers/quran_provider.dart';
import '../utils/date_utils.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer4<
      ThemeProvider,
      RoutineProvider,
      BookProvider,
      QuranProvider
    >(
      builder: (
        context,
        themeProvider,
        routineProvider,
        bookProvider,
        quranProvider,
        child,
      ) {
        return Scaffold(
          body: SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildHeader(context, themeProvider),
                  const SizedBox(height: 24),
                  _buildProgressSection(
                    context,
                    themeProvider,
                    routineProvider,
                  ),
                  const SizedBox(height: 24),
                  _buildTodayTasksSection(
                    context,
                    themeProvider,
                    routineProvider,
                    bookProvider,
                    quranProvider,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context, ThemeProvider themeProvider) {
    final now = DateTime.now();
    final theme = themeProvider.currentTheme;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: theme.progressGradient,
          begin: Alignment.topRight,
          end: Alignment.bottomLeft,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'مرحباً بك في إنجاز',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: theme.category == 'night' ? Colors.white : Colors.black,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            AppDateUtils.formatHijriDate(now),
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color:
                  theme.category == 'night' ? Colors.white70 : Colors.black87,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            AppDateUtils.formatGregorianDate(now),
            style: TextStyle(
              fontSize: 14,
              color:
                  theme.category == 'night' ? Colors.white60 : Colors.black54,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressSection(
    BuildContext context,
    ThemeProvider themeProvider,
    RoutineProvider routineProvider,
  ) {
    final theme = themeProvider.currentTheme;
    final completionPercentage = routineProvider.getTodayCompletionPercentage();
    final completedCount = routineProvider.getCompletedTodayCount();
    final totalCount = routineProvider.getTodayRoutines().length;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إنجاز اليوم',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: theme.primaryText,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              height: 8,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                gradient: LinearGradient(
                  colors: [
                    theme.progressGradient.first.withValues(alpha: 0.3),
                    theme.progressGradient.last.withValues(alpha: 0.3),
                  ],
                ),
              ),
              child: FractionallySizedBox(
                alignment: Alignment.centerRight,
                widthFactor: completionPercentage / 100,
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4),
                    gradient: LinearGradient(
                      colors: theme.progressGradient,
                      begin: Alignment.centerRight,
                      end: Alignment.centerLeft,
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              totalCount > 0
                  ? '${completionPercentage.toStringAsFixed(0)}% من المهام اليومية ($completedCount من $totalCount)'
                  : 'لا توجد مهام لليوم',
              style: TextStyle(fontSize: 14, color: theme.secondaryText),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTodayTasksSection(
    BuildContext context,
    ThemeProvider themeProvider,
    RoutineProvider routineProvider,
    BookProvider bookProvider,
    QuranProvider quranProvider,
  ) {
    final theme = themeProvider.currentTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'مهام اليوم',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.primaryText,
          ),
        ),
        const SizedBox(height: 16),
        ..._buildTaskCards(theme, routineProvider, bookProvider, quranProvider),
      ],
    );
  }

  List<Widget> _buildTaskCards(
    theme,
    RoutineProvider routineProvider,
    BookProvider bookProvider,
    QuranProvider quranProvider,
  ) {
    final cards = <Widget>[];

    // Routines card
    final todayRoutines = routineProvider.getTodayRoutines();
    final completedRoutines = routineProvider.getCompletedTodayCount();
    if (todayRoutines.isNotEmpty) {
      cards.add(
        _buildTaskCard(
          'الروتينات',
          '$completedRoutines من ${todayRoutines.length} مهام مكتملة',
          Icons.repeat,
          theme,
          () {},
        ),
      );
      cards.add(const SizedBox(height: 12));
    }

    // Quran plans cards
    final quranTargets = quranProvider.getTodayTargets();
    if (quranTargets.isNotEmpty) {
      if (quranTargets.containsKey('memorization')) {
        cards.add(
          _buildTaskCard(
            'حفظ القرآن',
            'الهدف اليومي: ${quranTargets['memorization']} صفحة',
            Icons.book,
            theme,
            () {},
          ),
        );
        cards.add(const SizedBox(height: 12));
      }

      if (quranTargets.containsKey('recitation')) {
        cards.add(
          _buildTaskCard(
            'تلاوة القرآن',
            'الهدف اليومي: ${quranTargets['recitation']} صفحة',
            Icons.menu_book,
            theme,
            () {},
          ),
        );
        cards.add(const SizedBox(height: 12));
      }
    }

    // Active books card
    final activeBooks = bookProvider.getActiveBooks();
    if (activeBooks.isNotEmpty) {
      final book = activeBooks.first;
      cards.add(
        _buildTaskCard(
          'القراءة',
          '${book.title} - ${book.currentPage} من ${book.totalPages} صفحة',
          Icons.library_books,
          theme,
          () {},
        ),
      );
      cards.add(const SizedBox(height: 12));
    }

    // Remove last SizedBox if exists
    if (cards.isNotEmpty && cards.last is SizedBox) {
      cards.removeLast();
    }

    // Add message if no tasks
    if (cards.isEmpty) {
      cards.add(
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              'لا توجد مهام لليوم. ابدأ بإضافة روتينات أو خطط قرآنية أو كتب للقراءة.',
              textAlign: TextAlign.center,
              style: TextStyle(color: theme.secondaryText, fontSize: 16),
            ),
          ),
        ),
      );
    }

    return cards;
  }

  Widget _buildTaskCard(
    String title,
    String subtitle,
    IconData icon,
    theme,
    VoidCallback onTap,
  ) {
    return Card(
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: theme.progressGradient.first.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: theme.progressGradient.first),
        ),
        title: Text(
          title,
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: theme.primaryText,
          ),
        ),
        subtitle: Text(subtitle, style: TextStyle(color: theme.secondaryText)),
        trailing: Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: theme.secondaryText,
        ),
        onTap: onTap,
      ),
    );
  }
}
