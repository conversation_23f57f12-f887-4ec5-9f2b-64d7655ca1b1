class AppDateUtils {
  static const List<String> arabicMonths = [
    'يناير',
    'فبراير',
    'مارس',
    'أبريل',
    'مايو',
    'يونيو',
    'يوليو',
    'أغسطس',
    'سب<PERSON><PERSON><PERSON><PERSON>',
    'أكتوبر',
    'نوفمبر',
    'ديسمبر',
  ];

  static const List<String> arabicDays = [
    'الاثنين',
    'الثلاثاء',
    'الأربعاء',
    'الخميس',
    'الجمعة',
    'السبت',
    'الأحد',
  ];

  static const List<String> hijriMonths = [
    'محرم',
    'صفر',
    'ربيع الأول',
    'ربيع الثاني',
    'جمادى الأولى',
    'جمادى الثانية',
    'رجب',
    'شعبان',
    'رمضان',
    'شوال',
    'ذو القعدة',
    'ذو الحجة',
  ];

  static const List<String> hijriDays = [
    'الاثنين',
    'الثلاثاء',
    'الأربعاء',
    'الخميس',
    'الجمعة',
    'السبت',
    'الأحد',
  ];

  /// تحويل التاريخ الميلادي إلى نص عربي
  static String formatGregorianDate(DateTime date) {
    final dayName = arabicDays[date.weekday - 1];
    final monthName = arabicMonths[date.month - 1];
    return '$dayName، ${date.day} $monthName ${date.year}';
  }

  /// تحويل التاريخ الهجري إلى نص عربي (تنفيذ مؤقت)
  static String formatHijriDate(DateTime date) {
    // تنفيذ مؤقت - سيتم تحسينه لاحقاً
    final hijriDate = _approximateHijriDate(date);
    final dayName = hijriDays[date.weekday - 1];
    final monthName = hijriMonths[hijriDate['month']! - 1];
    return '$dayName، ${hijriDate['day']} $monthName ${hijriDate['year']}';
  }

  /// تحويل التاريخ الهجري إلى نص مختصر (تنفيذ مؤقت)
  static String formatHijriDateShort(DateTime date) {
    // تنفيذ مؤقت - سيتم تحسينه لاحقاً
    final hijriDate = _approximateHijriDate(date);
    final monthName = hijriMonths[hijriDate['month']! - 1];
    return '${hijriDate['day']} $monthName ${hijriDate['year']}';
  }

  /// تحويل تقريبي للتاريخ الهجري (مؤقت)
  static Map<String, int> _approximateHijriDate(DateTime gregorianDate) {
    // تحويل تقريبي بسيط - سيتم تحسينه لاحقاً
    const hijriEpoch = 227015; // 1 Muharram 1 AH in Julian days
    final julianDay = _gregorianToJulian(gregorianDate);
    final hijriDays = julianDay - hijriEpoch;
    final hijriYear = (hijriDays / 354.37).floor() + 1;
    final dayOfYear = hijriDays % 354.37;
    final hijriMonth = (dayOfYear / 29.53).floor() + 1;
    final hijriDay = (dayOfYear % 29.53).floor() + 1;

    return {
      'year': hijriYear,
      'month': hijriMonth.clamp(1, 12),
      'day': hijriDay.clamp(1, 30),
    };
  }

  /// تحويل التاريخ الميلادي إلى يوليان
  static int _gregorianToJulian(DateTime date) {
    final a = (14 - date.month) ~/ 12;
    final y = date.year + 4800 - a;
    final m = date.month + 12 * a - 3;
    return date.day +
        (153 * m + 2) ~/ 5 +
        365 * y +
        y ~/ 4 -
        y ~/ 100 +
        y ~/ 400 -
        32045;
  }

  /// تحويل التاريخ الميلادي إلى نص مختصر
  static String formatGregorianDateShort(DateTime date) {
    final monthName = arabicMonths[date.month - 1];
    return '${date.day} $monthName ${date.year}';
  }

  /// الحصول على اسم اليوم بالعربية
  static String getArabicDayName(DateTime date) {
    return arabicDays[date.weekday - 1];
  }

  /// الحصول على اسم الشهر الميلادي بالعربية
  static String getArabicMonthName(int month) {
    return arabicMonths[month - 1];
  }

  /// الحصول على اسم الشهر الهجري بالعربية
  static String getHijriMonthName(int month) {
    return hijriMonths[month - 1];
  }

  /// تحويل الأرقام الإنجليزية إلى عربية
  static String toArabicNumbers(String input) {
    const english = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    const arabic = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];

    String result = input;
    for (int i = 0; i < english.length; i++) {
      result = result.replaceAll(english[i], arabic[i]);
    }
    return result;
  }

  /// تحويل الأرقام العربية إلى إنجليزية
  static String toEnglishNumbers(String input) {
    const arabic = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    const english = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

    String result = input;
    for (int i = 0; i < arabic.length; i++) {
      result = result.replaceAll(arabic[i], english[i]);
    }
    return result;
  }

  /// التحقق من كون التاريخ اليوم
  static bool isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }

  /// التحقق من كون التاريخ أمس
  static bool isYesterday(DateTime date) {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return date.year == yesterday.year &&
        date.month == yesterday.month &&
        date.day == yesterday.day;
  }

  /// التحقق من كون التاريخ غداً
  static bool isTomorrow(DateTime date) {
    final tomorrow = DateTime.now().add(const Duration(days: 1));
    return date.year == tomorrow.year &&
        date.month == tomorrow.month &&
        date.day == tomorrow.day;
  }

  /// الحصول على بداية اليوم
  static DateTime startOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day);
  }

  /// الحصول على نهاية اليوم
  static DateTime endOfDay(DateTime date) {
    return DateTime(date.year, date.month, date.day, 23, 59, 59, 999);
  }

  /// الحصول على الفرق بين تاريخين بالأيام
  static int daysBetween(DateTime from, DateTime to) {
    from = DateTime(from.year, from.month, from.day);
    to = DateTime(to.year, to.month, to.day);
    return (to.difference(from).inHours / 24).round();
  }

  /// تنسيق الوقت بالعربية
  static String formatTime(DateTime time) {
    final hour = time.hour;
    final minute = time.minute;
    final period = hour >= 12 ? 'مساءً' : 'صباحاً';
    final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);

    return '${displayHour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $period';
  }

  /// تنسيق المدة الزمنية بالعربية
  static String formatDuration(Duration duration) {
    if (duration.inDays > 0) {
      return '${duration.inDays} يوم';
    } else if (duration.inHours > 0) {
      return '${duration.inHours} ساعة';
    } else if (duration.inMinutes > 0) {
      return '${duration.inMinutes} دقيقة';
    } else {
      return '${duration.inSeconds} ثانية';
    }
  }

  /// الحصول على التاريخ النسبي (اليوم، أمس، إلخ)
  static String getRelativeDate(DateTime date) {
    if (isToday(date)) {
      return 'اليوم';
    } else if (isYesterday(date)) {
      return 'أمس';
    } else if (isTomorrow(date)) {
      return 'غداً';
    } else {
      final daysDiff = daysBetween(DateTime.now(), date);
      if (daysDiff > 0 && daysDiff <= 7) {
        return 'خلال $daysDiff أيام';
      } else if (daysDiff < 0 && daysDiff >= -7) {
        return 'منذ ${-daysDiff} أيام';
      } else {
        return formatGregorianDateShort(date);
      }
    }
  }
}
