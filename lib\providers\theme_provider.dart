import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../themes/app_themes.dart';

class ThemeProvider extends ChangeNotifier {
  AppTheme _currentTheme = AppThemes.dayThemes.first;
  static const String _themeKey = 'selected_theme';

  AppTheme get currentTheme => _currentTheme;
  ThemeData get themeData => _currentTheme.themeData;

  ThemeProvider() {
    _loadTheme();
  }

  Future<void> _loadTheme() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final themeId = prefs.getString(_themeKey);
      if (themeId != null) {
        _currentTheme = AppThemes.getThemeById(themeId);
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error loading theme: $e');
    }
  }

  Future<void> setTheme(AppTheme theme) async {
    try {
      _currentTheme = theme;
      notifyListeners();
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_themeKey, theme.id);
    } catch (e) {
      debugPrint('Error saving theme: $e');
    }
  }

  List<AppTheme> get dayThemes => AppThemes.dayThemesList;
  List<AppTheme> get nightThemes => AppThemes.nightThemesList;
  List<AppTheme> get allThemes => AppThemes.allThemes;
}
