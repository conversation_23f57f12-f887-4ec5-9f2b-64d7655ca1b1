import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:io';
import '../providers/theme_provider.dart';
import '../providers/book_provider.dart';
import '../models/book.dart';
import 'add_book_screen.dart';

class LibraryScreen extends StatefulWidget {
  const LibraryScreen({super.key});

  @override
  State<LibraryScreen> createState() => _LibraryScreenState();
}

class _LibraryScreenState extends State<LibraryScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<ThemeProvider, BookProvider>(
      builder: (context, themeProvider, bookProvider, child) {
        final theme = themeProvider.currentTheme;
        final activeBooks = bookProvider.getActiveBooks();
        final completedBooks = bookProvider.getCompletedBooks();

        return Scaffold(
          appBar: AppBar(
            title: Text(
              'مكتبتي',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: theme.primaryText,
              ),
            ),
            backgroundColor: theme.primaryBackground,
            elevation: 0,
            bottom: TabBar(
              controller: _tabController,
              tabs: [
                Tab(text: 'قيد القراءة (${activeBooks.length})'),
                Tab(text: 'مكتملة (${completedBooks.length})'),
              ],
              labelColor: theme.progressGradient.first,
              unselectedLabelColor: theme.secondaryText,
              indicatorColor: theme.progressGradient.first,
            ),
          ),
          body: TabBarView(
            controller: _tabController,
            children: [
              _buildBooksList(context, theme, activeBooks, bookProvider, false),
              _buildBooksList(
                context,
                theme,
                completedBooks,
                bookProvider,
                true,
              ),
            ],
          ),
          floatingActionButton: FloatingActionButton(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(builder: (context) => const AddBookScreen()),
              );
            },
            child: const Icon(Icons.add),
          ),
        );
      },
    );
  }

  Widget _buildBooksList(
    BuildContext context,
    theme,
    List<Book> books,
    BookProvider bookProvider,
    bool isCompleted,
  ) {
    if (books.isEmpty) {
      return _buildEmptyState(theme, isCompleted);
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: books.length,
      itemBuilder: (context, index) {
        final book = books[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                _buildBookCover(book, theme),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        book.title,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: theme.primaryText,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        book.author,
                        style: TextStyle(
                          fontSize: 14,
                          color: theme.secondaryText,
                        ),
                      ),
                      const SizedBox(height: 8),
                      _buildProgressBar(book, theme),
                      const SizedBox(height: 4),
                      Text(
                        '${book.currentPage} من ${book.totalPages} صفحة',
                        style: TextStyle(
                          fontSize: 12,
                          color: theme.secondaryText,
                        ),
                      ),
                    ],
                  ),
                ),
                PopupMenuButton<String>(
                  onSelected: (value) {
                    switch (value) {
                      case 'update_progress':
                        _showUpdateProgressDialog(
                          context,
                          book,
                          bookProvider,
                          theme,
                        );
                        break;
                      case 'edit':
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => AddBookScreen(book: book),
                          ),
                        );
                        break;
                      case 'delete':
                        _showDeleteDialog(context, book, bookProvider);
                        break;
                    }
                  },
                  itemBuilder:
                      (context) => [
                        if (!isCompleted)
                          const PopupMenuItem(
                            value: 'update_progress',
                            child: Text('تحديث التقدم'),
                          ),
                        const PopupMenuItem(
                          value: 'edit',
                          child: Text('تعديل'),
                        ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: Text('حذف'),
                        ),
                      ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildEmptyState(theme, bool isCompleted) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            isCompleted ? Icons.check_circle : Icons.library_books,
            size: 80,
            color: theme.secondaryText,
          ),
          const SizedBox(height: 16),
          Text(
            isCompleted ? 'لا توجد كتب مكتملة بعد' : 'لا توجد كتب قيد القراءة',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: theme.primaryText,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            isCompleted
                ? 'أكمل قراءة كتاب لتراه هنا'
                : 'ابدأ بإضافة كتاب جديد لتتبع تقدمك في القراءة',
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 16, color: theme.secondaryText),
          ),
        ],
      ),
    );
  }

  Widget _buildBookCover(Book book, theme) {
    return Container(
      width: 60,
      height: 80,
      decoration: BoxDecoration(
        color: theme.cardBackground,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: theme.borderColor),
      ),
      child:
          book.coverImagePath != null
              ? ClipRRect(
                borderRadius: BorderRadius.circular(6),
                child: Image.file(
                  File(book.coverImagePath!),
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return _buildBookPlaceholder(theme);
                  },
                ),
              )
              : _buildBookPlaceholder(theme),
    );
  }

  Widget _buildBookPlaceholder(theme) {
    return Icon(Icons.book, size: 30, color: theme.secondaryText);
  }

  Widget _buildProgressBar(Book book, theme) {
    final progress = book.progressPercentage / 100;

    return Container(
      height: 6,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(3),
        color: theme.progressGradient.first.withValues(alpha: 0.3),
      ),
      child: FractionallySizedBox(
        alignment: Alignment.centerRight,
        widthFactor: progress,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(3),
            gradient: LinearGradient(
              colors: theme.progressGradient,
              begin: Alignment.centerRight,
              end: Alignment.centerLeft,
            ),
          ),
        ),
      ),
    );
  }

  void _showUpdateProgressDialog(
    BuildContext context,
    Book book,
    BookProvider bookProvider,
    theme,
  ) {
    final controller = TextEditingController(text: book.currentPage.toString());

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تحديث التقدم'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('الكتاب: ${book.title}'),
                const SizedBox(height: 16),
                TextFormField(
                  controller: controller,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    labelText: 'الصفحة الحالية',
                    hintText: 'من 0 إلى ${book.totalPages}',
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () async {
                  final newPage = int.tryParse(controller.text);
                  if (newPage != null &&
                      newPage >= 0 &&
                      newPage <= book.totalPages) {
                    await bookProvider.updateProgress(book.id, newPage);
                    if (context.mounted) {
                      Navigator.of(context).pop();
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('تم تحديث التقدم بنجاح')),
                      );
                    }
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('يرجى إدخال رقم صفحة صحيح')),
                    );
                  }
                },
                child: const Text('حفظ'),
              ),
            ],
          ),
    );
  }

  void _showDeleteDialog(
    BuildContext context,
    Book book,
    BookProvider bookProvider,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('حذف الكتاب'),
            content: Text('هل أنت متأكد من حذف كتاب "${book.title}"؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () async {
                  await bookProvider.deleteBook(book.id);
                  if (context.mounted) {
                    Navigator.of(context).pop();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('تم حذف الكتاب بنجاح')),
                    );
                  }
                },
                child: const Text('حذف'),
              ),
            ],
          ),
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
}
