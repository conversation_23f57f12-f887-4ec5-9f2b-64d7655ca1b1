import 'package:flutter/material.dart';
import '../models/routine.dart';
import '../services/database_service.dart';

class RoutineProvider extends ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();
  List<Routine> _routines = [];
  List<RoutineProgress> _todayProgress = [];
  bool _isLoading = false;

  List<Routine> get routines => _routines;
  List<RoutineProgress> get todayProgress => _todayProgress;
  bool get isLoading => _isLoading;

  RoutineProvider() {
    loadRoutines();
    loadTodayProgress();
  }

  Future<void> loadRoutines() async {
    _isLoading = true;
    notifyListeners();

    try {
      _routines = await _databaseService.getRoutines();
    } catch (e) {
      debugPrint('Error loading routines: $e');
    }

    _isLoading = false;
    notifyListeners();
  }

  Future<void> loadTodayProgress() async {
    try {
      final today = DateTime.now();
      _todayProgress.clear();
      
      for (final routine in _routines) {
        final progress = await _databaseService.getRoutineProgress(routine.id, today);
        if (progress.isNotEmpty) {
          _todayProgress.addAll(progress);
        }
      }
    } catch (e) {
      debugPrint('Error loading today progress: $e');
    }
    
    notifyListeners();
  }

  Future<void> addRoutine(Routine routine) async {
    try {
      await _databaseService.insertRoutine(routine);
      _routines.add(routine);
      notifyListeners();
    } catch (e) {
      debugPrint('Error adding routine: $e');
    }
  }

  Future<void> updateRoutine(Routine routine) async {
    try {
      await _databaseService.updateRoutine(routine);
      final index = _routines.indexWhere((r) => r.id == routine.id);
      if (index != -1) {
        _routines[index] = routine;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error updating routine: $e');
    }
  }

  Future<void> deleteRoutine(String routineId) async {
    try {
      await _databaseService.deleteRoutine(routineId);
      _routines.removeWhere((r) => r.id == routineId);
      _todayProgress.removeWhere((p) => p.routineId == routineId);
      notifyListeners();
    } catch (e) {
      debugPrint('Error deleting routine: $e');
    }
  }

  Future<void> updateProgress(RoutineProgress progress) async {
    try {
      final existingIndex = _todayProgress.indexWhere(
        (p) => p.routineId == progress.routineId && 
               p.date.day == progress.date.day &&
               p.date.month == progress.date.month &&
               p.date.year == progress.date.year,
      );

      if (existingIndex != -1) {
        await _databaseService.updateRoutineProgress(progress);
        _todayProgress[existingIndex] = progress;
      } else {
        await _databaseService.insertRoutineProgress(progress);
        _todayProgress.add(progress);
      }
      
      notifyListeners();
    } catch (e) {
      debugPrint('Error updating progress: $e');
    }
  }

  List<Routine> getTodayRoutines() {
    final today = DateTime.now();
    final dayOfWeek = today.weekday; // 1 = Monday, 7 = Sunday
    
    return _routines.where((routine) {
      if (!routine.isActive) return false;
      
      if (routine.frequency == 'daily') {
        return true;
      } else {
        return routine.selectedDays.contains(dayOfWeek);
      }
    }).toList();
  }

  int getCompletedTodayCount() {
    final todayRoutines = getTodayRoutines();
    int completed = 0;
    
    for (final routine in todayRoutines) {
      final progress = _todayProgress.where(
        (p) => p.routineId == routine.id && p.isCompleted,
      ).toList();
      
      if (progress.isNotEmpty) {
        completed++;
      }
    }
    
    return completed;
  }

  double getTodayCompletionPercentage() {
    final todayRoutines = getTodayRoutines();
    if (todayRoutines.isEmpty) return 0.0;
    
    final completed = getCompletedTodayCount();
    return (completed / todayRoutines.length) * 100;
  }

  RoutineProgress? getRoutineProgress(String routineId) {
    return _todayProgress.where((p) => p.routineId == routineId).firstOrNull;
  }
}
