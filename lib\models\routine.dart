class Routine {
  final String id;
  final String name;
  final String category; // صحي، تعليمي، ديني، عادة
  final String measureType; // checkbox, counter, timer
  final double targetValue;
  final String unit; // دقيقة، صفحة، كوب، etc.
  final String frequency; // daily, custom
  final List<int> selectedDays; // 1-7 (Monday-Sunday)
  final bool isActive;
  final DateTime createdAt;
  final DateTime? updatedAt;

  Routine({
    required this.id,
    required this.name,
    required this.category,
    required this.measureType,
    required this.targetValue,
    required this.unit,
    required this.frequency,
    required this.selectedDays,
    this.isActive = true,
    required this.createdAt,
    this.updatedAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'category': category,
      'measureType': measureType,
      'targetValue': targetValue,
      'unit': unit,
      'frequency': frequency,
      'selectedDays': selectedDays,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  factory Routine.fromJson(Map<String, dynamic> json) {
    return Routine(
      id: json['id'],
      name: json['name'],
      category: json['category'],
      measureType: json['measureType'],
      targetValue: json['targetValue'].toDouble(),
      unit: json['unit'],
      frequency: json['frequency'],
      selectedDays: List<int>.from(json['selectedDays']),
      isActive: json['isActive'] ?? true,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
    );
  }

  Routine copyWith({
    String? id,
    String? name,
    String? category,
    String? measureType,
    double? targetValue,
    String? unit,
    String? frequency,
    List<int>? selectedDays,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Routine(
      id: id ?? this.id,
      name: name ?? this.name,
      category: category ?? this.category,
      measureType: measureType ?? this.measureType,
      targetValue: targetValue ?? this.targetValue,
      unit: unit ?? this.unit,
      frequency: frequency ?? this.frequency,
      selectedDays: selectedDays ?? this.selectedDays,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

class RoutineProgress {
  final String id;
  final String routineId;
  final DateTime date;
  final double currentValue;
  final bool isCompleted;
  final DateTime createdAt;
  final DateTime? updatedAt;

  RoutineProgress({
    required this.id,
    required this.routineId,
    required this.date,
    required this.currentValue,
    required this.isCompleted,
    required this.createdAt,
    this.updatedAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'routineId': routineId,
      'date': date.toIso8601String(),
      'currentValue': currentValue,
      'isCompleted': isCompleted,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  factory RoutineProgress.fromJson(Map<String, dynamic> json) {
    return RoutineProgress(
      id: json['id'],
      routineId: json['routineId'],
      date: DateTime.parse(json['date']),
      currentValue: json['currentValue'].toDouble(),
      isCompleted: json['isCompleted'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
    );
  }

  RoutineProgress copyWith({
    String? id,
    String? routineId,
    DateTime? date,
    double? currentValue,
    bool? isCompleted,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return RoutineProgress(
      id: id ?? this.id,
      routineId: routineId ?? this.routineId,
      date: date ?? this.date,
      currentValue: currentValue ?? this.currentValue,
      isCompleted: isCompleted ?? this.isCompleted,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
