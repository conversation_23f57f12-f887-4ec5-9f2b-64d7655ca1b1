class QuranPage {
  final int pageNumber;
  final int juzNumber;
  final bool isRecited;
  final bool isMemorized;
  final bool isReviewed;
  final DateTime? recitedDate;
  final DateTime? memorizedDate;
  final DateTime? reviewedDate;

  QuranPage({
    required this.pageNumber,
    required this.juzNumber,
    this.isRecited = false,
    this.isMemorized = false,
    this.isReviewed = false,
    this.recitedDate,
    this.memorizedDate,
    this.reviewedDate,
  });

  Map<String, dynamic> toJson() {
    return {
      'pageNumber': pageNumber,
      'juzNumber': juzNumber,
      'isRecited': isRecited,
      'isMemorized': isMemorized,
      'isReviewed': isReviewed,
      'recitedDate': recitedDate?.toIso8601String(),
      'memorizedDate': memorizedDate?.toIso8601String(),
      'reviewedDate': reviewedDate?.toIso8601String(),
    };
  }

  factory QuranPage.fromJson(Map<String, dynamic> json) {
    return QuranPage(
      pageNumber: json['pageNumber'],
      juzNumber: json['juzNumber'],
      isRecited: json['isRecited'] ?? false,
      isMemorized: json['isMemorized'] ?? false,
      isReviewed: json['isReviewed'] ?? false,
      recitedDate: json['recitedDate'] != null ? DateTime.parse(json['recitedDate']) : null,
      memorizedDate: json['memorizedDate'] != null ? DateTime.parse(json['memorizedDate']) : null,
      reviewedDate: json['reviewedDate'] != null ? DateTime.parse(json['reviewedDate']) : null,
    );
  }

  QuranPage copyWith({
    int? pageNumber,
    int? juzNumber,
    bool? isRecited,
    bool? isMemorized,
    bool? isReviewed,
    DateTime? recitedDate,
    DateTime? memorizedDate,
    DateTime? reviewedDate,
  }) {
    return QuranPage(
      pageNumber: pageNumber ?? this.pageNumber,
      juzNumber: juzNumber ?? this.juzNumber,
      isRecited: isRecited ?? this.isRecited,
      isMemorized: isMemorized ?? this.isMemorized,
      isReviewed: isReviewed ?? this.isReviewed,
      recitedDate: recitedDate ?? this.recitedDate,
      memorizedDate: memorizedDate ?? this.memorizedDate,
      reviewedDate: reviewedDate ?? this.reviewedDate,
    );
  }
}

class QuranPlan {
  final String id;
  final String type; // recitation, memorization, review
  final int dailyTarget; // pages per day
  final bool isActive;
  final DateTime createdAt;
  final DateTime? updatedAt;

  QuranPlan({
    required this.id,
    required this.type,
    required this.dailyTarget,
    this.isActive = true,
    required this.createdAt,
    this.updatedAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'dailyTarget': dailyTarget,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  factory QuranPlan.fromJson(Map<String, dynamic> json) {
    return QuranPlan(
      id: json['id'],
      type: json['type'],
      dailyTarget: json['dailyTarget'],
      isActive: json['isActive'] ?? true,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
    );
  }

  QuranPlan copyWith({
    String? id,
    String? type,
    int? dailyTarget,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return QuranPlan(
      id: id ?? this.id,
      type: type ?? this.type,
      dailyTarget: dailyTarget ?? this.dailyTarget,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

class QuranJuz {
  final int number;
  final int startPage;
  final int endPage;
  final String name;

  const QuranJuz({
    required this.number,
    required this.startPage,
    required this.endPage,
    required this.name,
  });

  int get totalPages => endPage - startPage + 1;

  static const List<QuranJuz> allJuz = [
    QuranJuz(number: 1, startPage: 1, endPage: 22, name: 'الفاتحة'),
    QuranJuz(number: 2, startPage: 23, endPage: 42, name: 'سيقول'),
    QuranJuz(number: 3, startPage: 43, endPage: 62, name: 'تلك الرسل'),
    QuranJuz(number: 4, startPage: 63, endPage: 82, name: 'لن تنالوا'),
    QuranJuz(number: 5, startPage: 83, endPage: 102, name: 'والمحصنات'),
    QuranJuz(number: 6, startPage: 103, endPage: 122, name: 'لا يحب الله'),
    QuranJuz(number: 7, startPage: 123, endPage: 142, name: 'وإذا سمعوا'),
    QuranJuz(number: 8, startPage: 143, endPage: 162, name: 'ولو أننا'),
    QuranJuz(number: 9, startPage: 163, endPage: 182, name: 'قال الملأ'),
    QuranJuz(number: 10, startPage: 183, endPage: 202, name: 'واعلموا'),
    QuranJuz(number: 11, startPage: 203, endPage: 222, name: 'يعتذرون'),
    QuranJuz(number: 12, startPage: 223, endPage: 242, name: 'وما من دابة'),
    QuranJuz(number: 13, startPage: 243, endPage: 262, name: 'وما أبرئ'),
    QuranJuz(number: 14, startPage: 263, endPage: 282, name: 'ربما'),
    QuranJuz(number: 15, startPage: 283, endPage: 302, name: 'سبحان الذي'),
    QuranJuz(number: 16, startPage: 303, endPage: 322, name: 'قال ألم'),
    QuranJuz(number: 17, startPage: 323, endPage: 342, name: 'اقترب للناس'),
    QuranJuz(number: 18, startPage: 343, endPage: 362, name: 'قد أفلح'),
    QuranJuz(number: 19, startPage: 363, endPage: 382, name: 'وقال الذين'),
    QuranJuz(number: 20, startPage: 383, endPage: 402, name: 'أمن خلق'),
    QuranJuz(number: 21, startPage: 403, endPage: 422, name: 'اتل ما أوحي'),
    QuranJuz(number: 22, startPage: 423, endPage: 442, name: 'ومن يقنت'),
    QuranJuz(number: 23, startPage: 443, endPage: 462, name: 'وما لي'),
    QuranJuz(number: 24, startPage: 463, endPage: 482, name: 'فمن أظلم'),
    QuranJuz(number: 25, startPage: 483, endPage: 502, name: 'إليه يرد'),
    QuranJuz(number: 26, startPage: 503, endPage: 522, name: 'حم'),
    QuranJuz(number: 27, startPage: 523, endPage: 542, name: 'قال فما خطبكم'),
    QuranJuz(number: 28, startPage: 543, endPage: 562, name: 'قد سمع'),
    QuranJuz(number: 29, startPage: 563, endPage: 582, name: 'تبارك الذي'),
    QuranJuz(number: 30, startPage: 583, endPage: 604, name: 'عم'),
  ];

  static QuranJuz getJuzByNumber(int number) {
    return allJuz.firstWhere((juz) => juz.number == number);
  }

  static QuranJuz getJuzByPage(int pageNumber) {
    return allJuz.firstWhere(
      (juz) => pageNumber >= juz.startPage && pageNumber <= juz.endPage,
      orElse: () => allJuz.first,
    );
  }
}
