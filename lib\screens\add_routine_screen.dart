import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';
import '../providers/routine_provider.dart';
import '../models/routine.dart';
import '../utils/date_utils.dart';

class AddRoutineScreen extends StatefulWidget {
  final Routine? routine; // For editing existing routine

  const AddRoutineScreen({super.key, this.routine});

  @override
  State<AddRoutineScreen> createState() => _AddRoutineScreenState();
}

class _AddRoutineScreenState extends State<AddRoutineScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _targetValueController = TextEditingController();
  final _unitController = TextEditingController();

  String _selectedCategory = 'صحي';
  String _selectedMeasureType = 'checkbox';
  String _selectedFrequency = 'daily';
  List<int> _selectedDays = [];

  final List<String> _categories = ['صحي', 'تعليمي', 'ديني', 'عادة'];
  final List<String> _measureTypes = ['checkbox', 'counter', 'timer'];
  final Map<String, String> _measureTypeNames = {
    'checkbox': 'خانة اختيار',
    'counter': 'عداد',
    'timer': 'مؤقت',
  };

  final List<String> _dayNames = [
    'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد'
  ];

  @override
  void initState() {
    super.initState();
    if (widget.routine != null) {
      _loadRoutineData();
    }
  }

  void _loadRoutineData() {
    final routine = widget.routine!;
    _nameController.text = routine.name;
    _selectedCategory = routine.category;
    _selectedMeasureType = routine.measureType;
    _targetValueController.text = routine.targetValue.toString();
    _unitController.text = routine.unit;
    _selectedFrequency = routine.frequency;
    _selectedDays = List.from(routine.selectedDays);
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<ThemeProvider, RoutineProvider>(
      builder: (context, themeProvider, routineProvider, child) {
        final theme = themeProvider.currentTheme;
        
        return Scaffold(
          appBar: AppBar(
            title: Text(
              widget.routine != null ? 'تعديل الروتين' : 'إضافة روتين جديد',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: theme.primaryText,
              ),
            ),
            backgroundColor: theme.primaryBackground,
            elevation: 0,
            actions: [
              TextButton(
                onPressed: _saveRoutine,
                child: Text(
                  'حفظ',
                  style: TextStyle(
                    color: theme.progressGradient.first,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          body: Form(
            key: _formKey,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildNameField(theme),
                  const SizedBox(height: 16),
                  _buildCategoryField(theme),
                  const SizedBox(height: 16),
                  _buildMeasureTypeField(theme),
                  const SizedBox(height: 16),
                  if (_selectedMeasureType != 'checkbox') ...[
                    _buildTargetValueField(theme),
                    const SizedBox(height: 16),
                    _buildUnitField(theme),
                    const SizedBox(height: 16),
                  ],
                  _buildFrequencyField(theme),
                  const SizedBox(height: 16),
                  if (_selectedFrequency == 'custom') ...[
                    _buildDaysSelection(theme),
                    const SizedBox(height: 16),
                  ],
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildNameField(theme) {
    return TextFormField(
      controller: _nameController,
      decoration: InputDecoration(
        labelText: 'اسم الروتين',
        hintText: 'مثال: شرب الماء، قراءة كتاب، رياضة',
        labelStyle: TextStyle(color: theme.primaryText),
        hintStyle: TextStyle(color: theme.secondaryText),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'يرجى إدخال اسم الروتين';
        }
        return null;
      },
    );
  }

  Widget _buildCategoryField(theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الفئة',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: theme.primaryText,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          children: _categories.map((category) {
            final isSelected = _selectedCategory == category;
            return FilterChip(
              label: Text(category),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _selectedCategory = category;
                });
              },
              selectedColor: theme.progressGradient.first.withValues(alpha: 0.2),
              checkmarkColor: theme.progressGradient.first,
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildMeasureTypeField(theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نوع المقياس',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: theme.primaryText,
          ),
        ),
        const SizedBox(height: 8),
        ...(_measureTypes.map((type) {
          return RadioListTile<String>(
            title: Text(
              _measureTypeNames[type]!,
              style: TextStyle(color: theme.primaryText),
            ),
            value: type,
            groupValue: _selectedMeasureType,
            onChanged: (value) {
              setState(() {
                _selectedMeasureType = value!;
              });
            },
            activeColor: theme.progressGradient.first,
          );
        }).toList()),
      ],
    );
  }

  Widget _buildTargetValueField(theme) {
    return TextFormField(
      controller: _targetValueController,
      keyboardType: TextInputType.number,
      decoration: InputDecoration(
        labelText: 'القيمة المستهدفة',
        hintText: 'مثال: 30، 10، 5',
        labelStyle: TextStyle(color: theme.primaryText),
        hintStyle: TextStyle(color: theme.secondaryText),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'يرجى إدخال القيمة المستهدفة';
        }
        if (double.tryParse(value) == null) {
          return 'يرجى إدخال رقم صحيح';
        }
        return null;
      },
    );
  }

  Widget _buildUnitField(theme) {
    return TextFormField(
      controller: _unitController,
      decoration: InputDecoration(
        labelText: 'الوحدة',
        hintText: 'مثال: دقيقة، صفحة، كوب',
        labelStyle: TextStyle(color: theme.primaryText),
        hintStyle: TextStyle(color: theme.secondaryText),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'يرجى إدخال الوحدة';
        }
        return null;
      },
    );
  }

  Widget _buildFrequencyField(theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'التكرار',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: theme.primaryText,
          ),
        ),
        const SizedBox(height: 8),
        RadioListTile<String>(
          title: Text(
            'يومياً',
            style: TextStyle(color: theme.primaryText),
          ),
          value: 'daily',
          groupValue: _selectedFrequency,
          onChanged: (value) {
            setState(() {
              _selectedFrequency = value!;
              _selectedDays.clear();
            });
          },
          activeColor: theme.progressGradient.first,
        ),
        RadioListTile<String>(
          title: Text(
            'أيام محددة',
            style: TextStyle(color: theme.primaryText),
          ),
          value: 'custom',
          groupValue: _selectedFrequency,
          onChanged: (value) {
            setState(() {
              _selectedFrequency = value!;
            });
          },
          activeColor: theme.progressGradient.first,
        ),
      ],
    );
  }

  Widget _buildDaysSelection(theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'اختر الأيام',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: theme.primaryText,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          children: List.generate(7, (index) {
            final dayIndex = index + 1; // 1-7 for Monday-Sunday
            final isSelected = _selectedDays.contains(dayIndex);
            return FilterChip(
              label: Text(_dayNames[index]),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  if (selected) {
                    _selectedDays.add(dayIndex);
                  } else {
                    _selectedDays.remove(dayIndex);
                  }
                });
              },
              selectedColor: theme.progressGradient.first.withValues(alpha: 0.2),
              checkmarkColor: theme.progressGradient.first,
            );
          }),
        ),
      ],
    );
  }

  void _saveRoutine() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedFrequency == 'custom' && _selectedDays.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار يوم واحد على الأقل'),
        ),
      );
      return;
    }

    final routineProvider = Provider.of<RoutineProvider>(context, listen: false);
    
    final routine = Routine(
      id: widget.routine?.id ?? DateTime.now().millisecondsSinceEpoch.toString(),
      name: _nameController.text.trim(),
      category: _selectedCategory,
      measureType: _selectedMeasureType,
      targetValue: _selectedMeasureType == 'checkbox' 
        ? 1.0 
        : double.parse(_targetValueController.text),
      unit: _selectedMeasureType == 'checkbox' 
        ? 'مرة' 
        : _unitController.text.trim(),
      frequency: _selectedFrequency,
      selectedDays: _selectedFrequency == 'daily' 
        ? [1, 2, 3, 4, 5, 6, 7] 
        : _selectedDays,
      createdAt: widget.routine?.createdAt ?? DateTime.now(),
      updatedAt: DateTime.now(),
    );

    try {
      if (widget.routine != null) {
        await routineProvider.updateRoutine(routine);
      } else {
        await routineProvider.addRoutine(routine);
      }
      
      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.routine != null 
                ? 'تم تحديث الروتين بنجاح' 
                : 'تم إضافة الروتين بنجاح',
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('حدث خطأ أثناء حفظ الروتين'),
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _targetValueController.dispose();
    _unitController.dispose();
    super.dispose();
  }
}
