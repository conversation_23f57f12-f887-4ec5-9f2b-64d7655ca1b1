import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';
import '../providers/routine_provider.dart';
import '../providers/book_provider.dart';
import '../providers/quran_provider.dart';
import '../services/database_service.dart';

class DataManagementScreen extends StatelessWidget {
  const DataManagementScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer4<ThemeProvider, RoutineProvider, BookProvider, QuranProvider>(
      builder: (context, themeProvider, routineProvider, bookProvider, quranProvider, child) {
        final theme = themeProvider.currentTheme;
        
        return Scaffold(
          appBar: AppBar(
            title: Text(
              'إدارة البيانات',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: theme.primaryText,
              ),
            ),
            backgroundColor: theme.primaryBackground,
            elevation: 0,
          ),
          body: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildStatisticsSection(theme, routineProvider, bookProvider, quranProvider),
                const SizedBox(height: 24),
                _buildDataActionsSection(context, theme, routineProvider, bookProvider, quranProvider),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatisticsSection(
    theme,
    RoutineProvider routineProvider,
    BookProvider bookProvider,
    QuranProvider quranProvider,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إحصائيات البيانات',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: theme.primaryText,
              ),
            ),
            const SizedBox(height: 16),
            _buildStatItem(
              'الروتينات',
              '${routineProvider.routines.length} روتين',
              Icons.repeat,
              theme,
            ),
            const SizedBox(height: 8),
            _buildStatItem(
              'الكتب',
              '${bookProvider.books.length} كتاب',
              Icons.library_books,
              theme,
            ),
            const SizedBox(height: 8),
            _buildStatItem(
              'صفحات القرآن المتلوة',
              '${quranProvider.getRecitedPagesCount()} صفحة',
              Icons.menu_book,
              theme,
            ),
            const SizedBox(height: 8),
            _buildStatItem(
              'صفحات القرآن المحفوظة',
              '${quranProvider.getMemorizedPagesCount()} صفحة',
              Icons.book,
              theme,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String title, String value, IconData icon, theme) {
    return Row(
      children: [
        Icon(
          icon,
          color: theme.progressGradient.first,
          size: 20,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            title,
            style: TextStyle(
              color: theme.primaryText,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Text(
          value,
          style: TextStyle(
            color: theme.secondaryText,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildDataActionsSection(
    BuildContext context,
    theme,
    RoutineProvider routineProvider,
    BookProvider bookProvider,
    QuranProvider quranProvider,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إجراءات البيانات',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: theme.primaryText,
              ),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: Icon(
                Icons.refresh,
                color: theme.progressGradient.first,
              ),
              title: Text(
                'تحديث البيانات',
                style: TextStyle(color: theme.primaryText),
              ),
              subtitle: Text(
                'إعادة تحميل جميع البيانات من قاعدة البيانات',
                style: TextStyle(color: theme.secondaryText),
              ),
              onTap: () => _refreshAllData(context, routineProvider, bookProvider, quranProvider),
            ),
            const Divider(),
            ListTile(
              leading: Icon(
                Icons.restart_alt,
                color: Colors.orange,
              ),
              title: Text(
                'إعادة تعيين تقدم القرآن',
                style: TextStyle(color: theme.primaryText),
              ),
              subtitle: Text(
                'مسح جميع علامات التلاوة والحفظ والمراجعة',
                style: TextStyle(color: theme.secondaryText),
              ),
              onTap: () => _showResetQuranDialog(context, quranProvider),
            ),
            const Divider(),
            ListTile(
              leading: Icon(
                Icons.delete_forever,
                color: Colors.red,
              ),
              title: Text(
                'مسح جميع البيانات',
                style: TextStyle(color: theme.primaryText),
              ),
              subtitle: Text(
                'حذف جميع الروتينات والكتب وتقدم القرآن',
                style: TextStyle(color: theme.secondaryText),
              ),
              onTap: () => _showClearAllDataDialog(context, routineProvider, bookProvider, quranProvider),
            ),
          ],
        ),
      ),
    );
  }

  void _refreshAllData(
    BuildContext context,
    RoutineProvider routineProvider,
    BookProvider bookProvider,
    QuranProvider quranProvider,
  ) async {
    try {
      await Future.wait([
        routineProvider.loadRoutines(),
        bookProvider.loadBooks(),
        quranProvider.loadPages(),
        quranProvider.loadPlans(),
      ]);
      
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث البيانات بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('حدث خطأ أثناء تحديث البيانات'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showResetQuranDialog(BuildContext context, QuranProvider quranProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعادة تعيين تقدم القرآن'),
        content: const Text(
          'هل أنت متأكد من إعادة تعيين جميع علامات التلاوة والحفظ والمراجعة؟\n\nهذا الإجراء لا يمكن التراجع عنه.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              try {
                // Reset all Quran pages
                for (int i = 1; i <= 604; i++) {
                  await quranProvider.updatePageStatus(i, 'recitation', false);
                  await quranProvider.updatePageStatus(i, 'memorization', false);
                  await quranProvider.updatePageStatus(i, 'review', false);
                }
                
                if (context.mounted) {
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم إعادة تعيين تقدم القرآن بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                if (context.mounted) {
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('حدث خطأ أثناء إعادة التعيين'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            child: const Text('إعادة تعيين', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _showClearAllDataDialog(
    BuildContext context,
    RoutineProvider routineProvider,
    BookProvider bookProvider,
    QuranProvider quranProvider,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مسح جميع البيانات'),
        content: const Text(
          'هل أنت متأكد من حذف جميع البيانات؟\n\nسيتم حذف:\n• جميع الروتينات\n• جميع الكتب\n• جميع تقدم القرآن\n\nهذا الإجراء لا يمكن التراجع عنه.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              try {
                await DatabaseService().clearAllData();
                
                // Refresh all providers
                await Future.wait([
                  routineProvider.loadRoutines(),
                  bookProvider.loadBooks(),
                  quranProvider.loadPages(),
                  quranProvider.loadPlans(),
                ]);
                
                if (context.mounted) {
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم مسح جميع البيانات بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                if (context.mounted) {
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('حدث خطأ أثناء مسح البيانات'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            child: const Text('مسح الكل', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}
